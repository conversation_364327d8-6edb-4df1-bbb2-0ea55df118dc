server:
  port: 8080

springdoc:
  swagger-ui:
   enabled: true
  api-docs:
   enabled: true

aws:
  sqs:
    consumer:
      enabled: true
      accessKey: ********************
      secretKey: qpnudiCgC0yGRs3ELEN6J+OXKNr6z06jhJ2cu5zm
      region: us-west-2
      queueUrl: https://sqs.us-west-2.amazonaws.com/194722441610/sparkview-dev
  s3:
    region: us-west-2
    bucket-name: sparkview-oregon-test
    access-key: ********************
    secret-key: pjM4B30OzZyi47mpd7/Wbvc/8vwRMlx2+hi1utr4
    endpoint: https://s3.us-west-2.amazonaws.com
    domain: https://sparkview-oregon-test.s3.us-west-2.amazonaws.com

workflow:
  output-prefix: /test/workflow_output/

spring:
  application:
    name: SparkView
  datasource:
    url: ****************************************************************************************
    driverClassName: org.postgresql.Driver
    username: sparkview
    password: RLCv6ClyrkDdRaK3Jm2i
  jpa:
    hibernate:
      ddl-auto: none

perfect:
  api:
    runDeploymentUrl: http://k8s-sparkvie-sparkvie-3de7fc993b-**********.us-west-2.elb.amazonaws.com/api/workflow/runDeploymentByName
    highlightUrl: http://localhost:8000/api/workflow/highlight
    dramaUrl: http://localhost:8000/api/workflow/drama
    acousticUrl: http://localhost:8000/api/workflow/acoustic
    effectUrl: http://localhost:8000/api/workflow/effect

firebase:
  config:
    path: classpath:firebase/serviceAccountKey-dev.json