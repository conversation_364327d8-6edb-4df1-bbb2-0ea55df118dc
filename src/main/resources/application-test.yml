server:
  port: 8080

spring:
  application:
    name: SparkView
  datasource:
    url: ****************************************************************************************
    driverClassName: org.postgresql.Driver
    username: sparkview
    password: RLCv6ClyrkDdRaK3Jm2i
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: update
    show-sql: true


springdoc:
  swagger-ui:
   enabled: true
  api-docs:
   enabled: true

workflow:
  output-prefix: /test/workflow_output/

sentry:
  enabled: ${SENTRY_ENABLED:false}
  dsn: https://<EMAIL>/4509116418883584
  send-default-pii: true
  logging:
    minimum-event-level: info
    minimum-breadcrumb-level: debug

perfect:
  api:
    runDeploymentUrl: http://sparkview-python/api/workflow/runDeploymentByName
    highlightUrl: http://sparkview-python/api/workflow/highlight
    dramaUrl: http://sparkview-python/api/workflow/drama
    dramaWithFilesUrl: http://sparkview-python/api/workflow/drama_with_files
    acousticUrl: http://sparkview-python/api/workflow/acoustic
    effectUrl: http://sparkview-python/api/workflow/effect

aws:
  sqs:
    consumer:
      enabled: true
      accessKey: ********************
      secretKey: qpnudiCgC0yGRs3ELEN6J+OXKNr6z06jhJ2cu5zm
      region: us-west-2
      queueUrl: https://sqs.us-west-2.amazonaws.com/************/sparkview-test
  s3:
    region: us-west-2
    bucket-name: sparkview-oregon-test
    access-key: ********************
    secret-key: pjM4B30OzZyi47mpd7/Wbvc/8vwRMlx2+hi1utr4
    endpoint: https://s3.us-west-2.amazonaws.com
    domain: https://sparkview-oregon-test.s3.us-west-2.amazonaws.com

firebase:
  config:
    path: classpath:firebase/serviceAccountKey-prod.json