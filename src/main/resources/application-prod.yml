server:
  port: 8080

springdoc:
  swagger-ui:
   enabled: true
  api-docs:
   enabled: true

workflow:
  output-prefix: /test/workflow_output/

spring:
  application:
    name: SparkView
  datasource:
    url: ****************************************************************************************
    driverClassName: org.postgresql.Driver
    username: sparkview
    password: RLCv6ClyrkDdRaK3Jm2i
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: update
    show-sql: true

perfect:
  api:
    runDeploymentUrl: http://k8s-sparkvie-sparkvie-3de7fc993b-1556711097.us-west-2.elb.amazonaws.com/api/workflow/runDeploymentByName
    highlightUrl: http://k8s-sparkvie-sparkvie-3de7fc993b-1556711097.us-west-2.elb.amazonaws.com/api/workflow/highlight
    dramaUrl: http://k8s-sparkvie-sparkvie-3de7fc993b-1556711097.us-west-2.elb.amazonaws.com/api/workflow/drama
    dramaWithFilesUrl: http://k8s-sparkvie-sparkvie-3de7fc993b-1556711097.us-west-2.elb.amazonaws.com/api/workflow/drama_with_files
    acousticUrl: http://k8s-sparkvie-sparkvie-3de7fc993b-1556711097.us-west-2.elb.amazonaws.com/api/workflow/acoustic
    effectUrl: http://k8s-sparkvie-sparkvie-3de7fc993b-1556711097.us-west-2.elb.amazonaws.com/api/workflow/effect

aws:
  sqs:
    consumer:
      enabled: true
      accessKey: ********************
      secretKey: qpnudiCgC0yGRs3ELEN6J+OXKNr6z06jhJ2cu5zm
      region: us-west-2
      queueUrl: https://sqs.us-west-2.amazonaws.com/194722441610/sparkview-console
  s3:
    region: us-west-2
    bucket-name: sparkview-oregon
    access-key: ********************
    secret-key: pjM4B30OzZyi47mpd7/Wbvc/8vwRMlx2+hi1utr4
    endpoint: https://s3.us-west-2.amazonaws.com
    domain: https://sparkview-oregon.s3.us-west-2.amazonaws.com

sentry:
  enabled: ${SENTRY_ENABLED:false}
  dsn: https://<EMAIL>/****************
  send-default-pii: true
  logging:
    minimum-event-level: info
    minimum-breadcrumb-level: debug

firebase:
  config:
    path: classpath:firebase/serviceAccountKey-prod.json