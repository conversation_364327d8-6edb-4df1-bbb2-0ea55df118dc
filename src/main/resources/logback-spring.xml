<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="LOG_PATH" value="${LOG_PATH:-logs}" />
    <property name="LOG_FILE" value="${LOG_FILE:-sparkview_java}" />
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 按小时滚动的文件日志 -->
    <appender name="HOURLY_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${LOG_FILE}.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按小时滚动，文件名格式为 sparkview-2023-01-01_12.log -->
            <fileNamePattern>${LOG_PATH}/${LOG_FILE}-%d{yyyy-MM-dd_HH}.log</fileNamePattern>
            <!-- 保留30天的历史日志 -->
            <maxHistory>720</maxHistory>
            <!-- 日志文件的总大小限制，超过后会删除旧的日志文件 -->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 错误日志单独保存 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${LOG_FILE}-error.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按小时滚动，文件名格式为 sparkview-error-2023-01-01_12.log -->
            <fileNamePattern>${LOG_PATH}/${LOG_FILE}-error-%d{yyyy-MM-dd_HH}.log</fileNamePattern>
            <!-- 保留30天的历史日志 -->
            <maxHistory>720</maxHistory>
            <!-- 日志文件的总大小限制，超过后会删除旧的日志文件 -->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步写入日志，提高性能 -->
    <appender name="ASYNC_HOURLY_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="HOURLY_FILE" />
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
    </appender>
    
    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ERROR_FILE" />
        <queueSize>256</queueSize>
        <discardingThreshold>0</discardingThreshold>
    </appender>
    
    <!-- Sentry集成 -->
    <appender name="SENTRY" class="io.sentry.logback.SentryAppender">
        <minimumEventLevel>ERROR</minimumEventLevel>
    </appender>
    
    <!-- 根日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="ASYNC_HOURLY_FILE" />
        <appender-ref ref="ASYNC_ERROR_FILE" />
        <appender-ref ref="SENTRY" />
    </root>
    
    <!-- 应用日志级别 -->
    <logger name="com.sparkview" level="INFO" />
    
    <!-- 第三方库日志级别 -->
    <logger name="org.springframework" level="INFO" />
    <logger name="org.hibernate" level="INFO" />
    <logger name="org.apache" level="WARN" />
</configuration> 