package com.sparkview.job;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.sparkview.common.ConfigTemplate;
import com.sparkview.common.FeishuUtil;
import com.sparkview.common.JsonUtils;
import com.sparkview.common.WorkflowInstanceStatus;
import com.sparkview.common.enums.VideoStatusEnum;
import com.sparkview.common.enums.WorkflowSourceEnum;
import com.sparkview.controller.FolderController;
import com.sparkview.persistence.ClipRecord;
import com.sparkview.persistence.Video;
import com.sparkview.persistence.WorkflowInstance;
import com.sparkview.repository.VideoRepository;
import com.sparkview.service.*;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.*;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Service
@ConditionalOnProperty(name = "aws.sqs.consumer.enabled", havingValue = "true")
public class SqsMessageConsumer {
    private SqsClient sqsClient;
    private ExecutorService executorService;
    private final AtomicBoolean running = new AtomicBoolean(false);

    // Cache to track retry counts for workflow instances
    private final ConcurrentHashMap<String, Integer> retryCountMap = new ConcurrentHashMap<>();
    // Cache to track when a retry was last attempted (for cleanup)
    private final ConcurrentHashMap<String, Long> retryTimestampMap = new ConcurrentHashMap<>();

    private static final int MAX_RETRY_COUNT = 3;
    // 24小时后清理retry记录
    private static final long RETRY_RECORD_TTL_MS = 24 * 60 * 60 * 1000;

    @Value("${aws.sqs.consumer.accessKey}")
    private String accessKey;

    @Value("${aws.sqs.consumer.secretKey}")
    private String secretKey;

    @Value("${aws.sqs.consumer.region}")
    private String region;

    @Value("${aws.sqs.consumer.queueUrl}")
    private String queueUrl;

    @Value("${feishu.webhook.url:https://open.feishu.cn/open-apis/bot/v2/hook/2553972c-7cd7-44e6-9707-73312d88c860}")
    private String feishuWebhookUrl;

    @Value("${feishu.secret:rCBYXx9UbIqmcs5gR7nvPg}")
    private String feishuSecret;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private RestTemplate restTemplate;

    @Resource
    private ClipRecordService clipRecordService;

    @Resource
    private HighlightService highlightService;

    @Resource
    private EffectsService effectsService;

    @Resource
    private VideoRepository videoRepository;

    @Resource
    private S3Service s3Service;

    @Resource
    private VideoService videoService;

    @Resource
    private DramaPerfectApiService dramaPerfectApiService;

    @Resource
    private FeishuUtil feishuUtil;

    @PostConstruct
    public void init() {
        // Initialize SQS client
        AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(accessKey, secretKey);
        StaticCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(awsCredentials);

        this.sqsClient = SqsClient.builder()
                .region(Region.of(region))
                .credentialsProvider(credentialsProvider)
                .build();

        // 增加线程池大小以适应更多的异步任务
        this.executorService = Executors.newFixedThreadPool(10);
        log.info("Initialized SQS consumer with thread pool size: 10");

        // Start message polling
        startConsumer();

        // 启动定期清理retry记录的任务
        startRetryMapCleanupTask();

        // 启动线程池监控任务
        startThreadPoolMonitorTask();
    }

    @PreDestroy
    public void cleanup() {
        stopConsumer();

        // Shutdown executor service
        if (executorService != null) {
            executorService.shutdown();
        }

        // Close SQS client
        if (sqsClient != null) {
            sqsClient.close();
        }
    }

    private void startConsumer() {
        if (running.compareAndSet(false, true)) {
            log.info("Starting SQS message consumer for queue: {}", queueUrl);
            // Start multiple consumer threads
            for (int i = 0; i < 2; i++) {
                executorService.submit(() -> {
                    log.info("Starting consumer thread: {}", Thread.currentThread().getName());
                    pollMessages();
                });
            }
        }
    }

    private void stopConsumer() {
        if (running.compareAndSet(true, false)) {
            log.info("Stopping SQS message consumer for queue: {}", queueUrl);
        }
    }

    private void pollMessages() {
        while (running.get()) {
            try {
                // Request messages from SQS queue
                ReceiveMessageRequest receiveRequest = ReceiveMessageRequest.builder()
                        .queueUrl(queueUrl)
                        .maxNumberOfMessages(10) // Batch size
                        .waitTimeSeconds(20) // Long polling
                        .visibilityTimeout(600) // 10分钟可见性超时，考虑到异步处理的时间
                        .build();

                ReceiveMessageResponse response = sqsClient.receiveMessage(receiveRequest);
                List<Message> messages = response.messages();

                if (!messages.isEmpty()) {
                    log.info("Received {} messages from SQS queue", messages.size());

                    // Process each message
                    for (Message message : messages) {
                        int retryCount = 0;
                        boolean processed = false;

                        while (!processed && retryCount < 3) { // Maximum 3 retries
                            try {
                                processMessage(message);
                                processed = true;

                                // Delete the message after successful processing
                                DeleteMessageRequest deleteRequest = DeleteMessageRequest.builder()
                                        .queueUrl(queueUrl)
                                        .receiptHandle(message.receiptHandle())
                                        .build();
                                sqsClient.deleteMessage(deleteRequest);
                                log.info("Successfully processed and deleted message: {}", message.messageId());

                            } catch (Exception e) {
                                retryCount++;
                                log.error("Error processing SQS message (attempt {}/3): {}, error: {}",
                                        retryCount, message.messageId(), e.getMessage(), e);

                                if (retryCount >= 3) {
                                    log.error("Failed to process message after {} retries: {}",
                                            retryCount, message.messageId());

                                    // 异步任务可能还在执行，这里尝试延长消息可见性超时时间，防止重复处理
                                    try {
                                        ChangeMessageVisibilityRequest visibilityRequest = ChangeMessageVisibilityRequest.builder()
                                                .queueUrl(queueUrl)
                                                .receiptHandle(message.receiptHandle())
                                                .visibilityTimeout(1800) // 延长到30分钟
                                                .build();
                                        sqsClient.changeMessageVisibility(visibilityRequest);
                                        log.info("Extended visibility timeout for message {} to 30 minutes", message.messageId());
                                    } catch (Exception ve) {
                                        log.error("Failed to extend visibility timeout: {}", ve.getMessage(), ve);
                                    }
                                } else {
                                    // Wait before retrying
                                    Thread.sleep(5000 * retryCount);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Error polling SQS queue: {}", e.getMessage(), e);
                try {
                    // Sleep briefly before retrying to avoid high CPU usage in error conditions
                    Thread.sleep(5000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        log.info("SQS consumer has stopped");
    }

    private void processMessage(Message message) {
        log.info("Processing SQS message: {}", message.messageId());
        try {
            String messageBody = message.body();
            log.info("SQS_event: {}", messageBody);
            JsonNode jsonNode = JsonUtils.getInstance().readTree(messageBody);
            if (jsonNode.has("workflow_instance_id") && jsonNode.has("event")) {
                String workflowInstanceId = jsonNode.get("workflow_instance_id").asText();
                String event = jsonNode.get("event").asText();
                log.info("Processing event: {} for workflow instance: {}", event, workflowInstanceId);

                if (event.equalsIgnoreCase("workflow_started") || event.equalsIgnoreCase("highlight_started") || event.equalsIgnoreCase("drama_started")) {
                    workflowService.updateWorkflowInstanceStatus(workflowInstanceId, WorkflowInstanceStatus.RUNNING);
                } else if (event.equalsIgnoreCase("workflow_completed")) {
                    completeWorkflow(workflowInstanceId, messageBody);
                } else if (event.equalsIgnoreCase("workflow_failed")) {
                    workflowService.updateWorkflowInstanceStatus(workflowInstanceId, WorkflowInstanceStatus.FAIL);
                } else if (event.equalsIgnoreCase("highlight_completed")) {
                    log.info("Processing highlight_completed event for workflow instance: {}", workflowInstanceId);
                    try {
                        processEffect(workflowInstanceId, jsonNode.get("data"));
                        log.info("Successfully processed effect for workflow instance: {}", workflowInstanceId);
                    } catch (Exception e) {
                        log.error("Failed to process effect for workflow instance: {}, error: {}", workflowInstanceId,
                                e.getMessage(), e);
                        throw e;
                    }
                } else if (event.equalsIgnoreCase("acoustic_completed") || event.equalsIgnoreCase("textover_completed")
                        || event.equalsIgnoreCase("vision_completed")) {
                    log.info("SQS: {}\t{}", event, workflowInstanceId);
                    // 异步更新高光信息
                    final JsonNode finalDataNode = jsonNode.get("data");
                    executorService.submit(() -> {
                        try {
                            highlightService.updateHighlight(finalDataNode);
                            log.info("Async updateHighlight finished for: {}", workflowInstanceId);
                        } catch (Exception e) {
                            log.error("Error in async updateHighlight: {}", e.getMessage(), e);
                        }
                    });
                } else if (event.equalsIgnoreCase("effect_completed")) {// effects处理
                    String data = jsonNode.get("data").asText();
                    log.info("SQS_effect_completed: {}\t{}\t{}", event, workflowInstanceId, data);
                    // 异步更新特效
                    final String finalData = data;
                    executorService.submit(() -> {
                        try {
                            effectsService.updateEffects(finalData);
                            processWorkflow(workflowInstanceId);
                            log.info("Async updateEffects and processWorkflow finished for: {}", workflowInstanceId);
                        } catch (Exception e) {
                            log.error("Error in async updateEffects: {}", e.getMessage(), e);
                        }
                    });
                } else if (event.equalsIgnoreCase("drama_completed")) {
                    // 异步处理最终视频文件object_key入库, 并更新状态为成功
                    final String finalMessageBody = messageBody;
                    executorService.submit(() -> {
                        try {
                            completeDramaWorkflow(workflowInstanceId, finalMessageBody);
                            log.info("Async completeDramaWorkflow finished for: {}", workflowInstanceId);
                        } catch (Exception e) {
                            log.error("Error in async completeDramaWorkflow: {}", e.getMessage(), e);
                        }
                    });
                } else if (event.equalsIgnoreCase("drama_with_files_completed")) {
                    // 处理带文件的短剧完成事件
                    completeDramaWithFilesWorkflow(workflowInstanceId, messageBody);
                } else if (event.equalsIgnoreCase("asr_completed")) {
                    // 处理ASR完成事件，更新视频的ASR路径
                    processAsrCompleted(workflowInstanceId, jsonNode);
                } else if (event.equalsIgnoreCase("workflow_retry")) {
                    // 异步处理提交短剧500的问题
                    final String finalWorkflowInstanceId = workflowInstanceId;
                    executorService.submit(() -> {
                        try {
                            retry(finalWorkflowInstanceId);
                        } catch (Exception e) {
                            log.error("Error during asynchronous retry execution for workflow {}: {}",
                                    finalWorkflowInstanceId, e.getMessage(), e);
                        }
                    });
                }
                log.info("Processing workflow instance: {}", workflowInstanceId);
            } else {
                log.warn("Invalid SQS message format, missing required fields: {}", messageBody);
            }
        } catch (JsonProcessingException e) {
            log.error("Error parsing SQS message body: {}", e.getMessage(), e);
        }
    }

    private void completeWorkflow(String workflowInstanceId, String messageBody) {
        CompleteMessage completeMessage = JsonUtils.toObject(messageBody, CompleteMessage.class);
        String outputJson = JsonUtils.toJson(completeMessage.getData());
        List<String> subtitles = completeMessage.getData().stream()
                .map(VideoDto::getSubtitle)
                .toList();
        workflowService.updateWorkflowInstanceStatus(workflowInstanceId, WorkflowInstanceStatus.SUCCESS, outputJson,
                JsonUtils.toJson(subtitles), completeMessage.getData().size());
    }

    private void completeDramaWorkflow(String workflowInstanceId, String messageBody) {
        CompleteDramaMessage completeDramaMessage = new Gson().fromJson(messageBody, CompleteDramaMessage.class);
        // 剪辑视频记录入库
        List<ClipRecord> clipRecordInfos = getClipRecordInfo(completeDramaMessage.getData().getPreClipList(),
                workflowInstanceId);
        clipRecordService.saveRecord(clipRecordInfos);

        // 文件名入库处理
        List<DramaClipVideo> result = completeDramaMessage.getData().getResult();
        List<VideoDto> list = result.stream().map(DramaClipVideo::getMetadata).toList();
        String outputJson = JsonUtils.toJson(list);
        workflowService.updateWorkflowInstanceStatus(workflowInstanceId, WorkflowInstanceStatus.SUCCESS, outputJson,
                null, result.size());
    }

    private List<ClipRecord> getClipRecordInfo(List<List<DramaClioPreClipInfo>> preClipList,
                                               String workflowInstanceId) {
        if (CollectionUtils.isEmpty(preClipList)) {
            log.error("getClipRecordInfo error, preClipList is empty. workflowInstanceId: {}", workflowInstanceId);
            return null;
        }

        List<ClipRecord> result = new ArrayList<>();

        for (List<DramaClioPreClipInfo> dramaClioPreClipInfos : preClipList) {
            if (CollectionUtils.isEmpty(dramaClioPreClipInfos)) {
                log.error("getClipRecordInfo error, dramaClioPreClipInfos is empty. workflowInstanceId: {}",
                        workflowInstanceId);
                continue;
            }

            ClipRecord clipRecord = getClipRecord(dramaClioPreClipInfos);
            if (clipRecord != null) {
                result.add(clipRecord);
            } else {
                log.error("clipRecord is null, skip this record. workflowInstanceId: {}", workflowInstanceId);
            }
        }

        return result;
    }

    private void processWorkflow(String workflowInstanceId) {
        Optional<WorkflowInstance> workflowInstanceOptional = workflowService
                .getWorkflowInstanceById(workflowInstanceId);
        if (workflowInstanceOptional.isEmpty()) {
            log.error("workflowInstance not found, workflowInstanceId: {}", workflowInstanceId);
        } else {
            WorkflowInstance workflowInstance = workflowInstanceOptional.get();
            int source = workflowInstance.getSource();
            // 高光特效和剪辑已经拆分开，所以在特效处理完，是不会触发剪辑
            if (source == WorkflowSourceEnum.CLIP.getCode()) {
                workflowService.updateHighlightWorkflowInstanceStatus(workflowInstanceId,
                        WorkflowInstanceStatus.SUCCESS, workflowInstance.getVideoCount());
            }
        }
    }

    private void processEffect(String workflowInstanceId, JsonNode dataNode) {
        Optional<WorkflowInstance> workflowInstanceOptional = workflowService
                .getWorkflowInstanceById(workflowInstanceId);
        if (workflowInstanceOptional.isEmpty()) {
            log.error("workflowInstance not found, workflowInstanceId: {}", workflowInstanceId);
        } else {
            // 更新视频状态
            try {
                List<String> videoIds = Lists.newArrayList();
                if (dataNode != null && dataNode.isArray()) {
                    for (JsonNode idNode : dataNode) {
                        videoIds.add(idNode.asText());
                    }
                }
                if (!videoIds.isEmpty())
                    videoRepository.updateVideosStatus(videoIds, VideoStatusEnum.SUCCEED.getCode());
            } catch (Exception e) {
                e.printStackTrace();
            }

            WorkflowInstance workflowInstance = workflowInstanceOptional.get();
            String params = workflowInstance.getParams();
            FolderController.AnalysisFolderRequest request = JsonUtils.toObject(params,
                    FolderController.AnalysisFolderRequest.class);

            String folderId = request.getFolderId();
            String uid = workflowInstance.getUid();
            int successCount = videoRepository.countByFolderIdAndStatusAndUid(folderId,
                    VideoStatusEnum.SUCCEED.getCode(), uid);
            int videoCount = workflowInstance.getVideoCount();
            if (successCount > 0 && successCount == videoCount) {
                highlightService.startEffectWorkflow(workflowInstanceId);
            } else {
                log.info("video processing in progress, waiting... workflowInstanceId: {}, success: {}, total: {}",
                        workflowInstanceId, successCount, videoCount);
            }
        }
    }

    private void retry(String workflowInstanceId) {
        // 记录重试时间戳
        retryTimestampMap.put(workflowInstanceId, System.currentTimeMillis());

        int retryCount = retryCountMap.compute(workflowInstanceId, (id, count) -> count == null ? 1 : count + 1);
        log.info("Processing retry for workflow: {}, retry count: {}", workflowInstanceId, retryCount);

        if (retryCount > MAX_RETRY_COUNT) {
            log.warn("Workflow {} has been retried more than {} times, sending Feishu alert",
                    workflowInstanceId, MAX_RETRY_COUNT);
            feishuUtil.sendFeishuAlert(workflowInstanceId, retryCount);
            return;
        }

        Optional<WorkflowInstance> workflowInstanceOptional = workflowService
                .getWorkflowInstanceById(workflowInstanceId);
        if (workflowInstanceOptional.isEmpty()) {
            log.error("retry workflowInstance not found, workflowInstanceId: {}", workflowInstanceId);
            // 工作流不存在，清理重试记录
            retryCountMap.remove(workflowInstanceId);
            retryTimestampMap.remove(workflowInstanceId);
        } else {
            WorkflowInstance workflowInstance = workflowInstanceOptional.get();
            String params = workflowInstance.getParams();
            ConfigTemplate configTemplate = JsonUtils.toObject(params, ConfigTemplate.class);
            String videoListStr = configTemplate.getValueByKey("videoList");
            if (StringUtils.isNotEmpty(videoListStr)) {
                List<String> videos = JsonUtils.toObject(videoListStr, new TypeReference<>() {
                });
                if (CollectionUtils.isNotEmpty(videos)) {
                    List<com.sparkview.persistence.Video> videoEntities = videoRepository.findAllByObjectKeyIn(videos);
                    log.info("Fetched {} video entities for retry processing.", videoEntities.size());

                    boolean allProcessed = true;
                    //处理duration
                    for (com.sparkview.persistence.Video videoEntity : videoEntities) {
                        double duration = videoEntity.getDuration();
                        if (duration == 0) {
                            try {
                                String objectKey = videoEntity.getObjectKey();
                                String presignedUrl = s3Service.generatePresignedDownloadUrl(objectKey, 10);
                                duration = videoService.getVideoDurationWithRetry(presignedUrl);
                                if (duration != 0) {
                                    videoEntity.setDuration(duration);
                                    videoRepository.save(videoEntity);
                                    log.info("Updated video entity with duration: {} {}", duration, objectKey);
                                } else {
                                    allProcessed = false;
                                    log.warn("Failed to get duration for video: {}", objectKey);
                                }
                            } catch (Exception e) {
                                allProcessed = false;
                                log.error("Error processing video duration: {}", e.getMessage(), e);
                            }
                        }
                    }

                    try {
                        //处理特效
                        highlightService.startEffectWorkflow(workflowInstanceId);
                        //处理剪辑
                        dramaPerfectApiService.startDramaWorkflow(workflowInstance);

                        // 如果所有处理都成功且没有异常，可以考虑清理重试记录
                        if (allProcessed) {
                            log.info("Successfully processed retry for workflow: {}, cleaning up retry record", workflowInstanceId);
                            // 处理成功后，只在最后一次重试时清理记录，避免清理过早
                            if (retryCount == MAX_RETRY_COUNT) {
                                retryCountMap.remove(workflowInstanceId);
                                retryTimestampMap.remove(workflowInstanceId);
                            }
                        }
                    } catch (Exception e) {
                        log.error("Error during retry workflow processing: {}", e.getMessage(), e);
                    }
                } else {
                    log.warn("No videos found in videoList for retry, cleaning up retry record");
                    retryCountMap.remove(workflowInstanceId);
                    retryTimestampMap.remove(workflowInstanceId);
                }
            } else {
                log.warn("Empty videoList in workflow params for retry, cleaning up retry record");
                retryCountMap.remove(workflowInstanceId);
                retryTimestampMap.remove(workflowInstanceId);
            }
        }
    }

    @NotNull
    private static ClipRecord getClipRecord(List<DramaClioPreClipInfo> dramaClioPreClipInfos) {
        String startVideoIdStr = dramaClioPreClipInfos.get(0).getVideoId();
        String endVideoIdStr = dramaClioPreClipInfos.get(dramaClioPreClipInfos.size() - 1).getVideoId();

        if (startVideoIdStr == null || endVideoIdStr == null) {
            log.error("getClipRecord error, videoId is null. startVideoId: {}, endVideoId: {}, dramaClioPreClipInfos: {}",
                    startVideoIdStr, endVideoIdStr, dramaClioPreClipInfos);
            return null;
        }

        ClipRecord clipRecord = new ClipRecord();
        clipRecord.setUid(dramaClioPreClipInfos.get(0).getUid());
        clipRecord.setStartTime(dramaClioPreClipInfos.get(0).getStart());
        clipRecord.setStartVideoId(Long.parseLong(startVideoIdStr));
        clipRecord.setEndTime(dramaClioPreClipInfos.get(dramaClioPreClipInfos.size() - 1).getEnd());
        clipRecord.setEndVideoId(Long.parseLong(endVideoIdStr));
        return clipRecord;
    }

    private void processAsrCompleted(String workflowInstanceId, JsonNode jsonNode) {
        log.info("处理ASR完成事件: {}", workflowInstanceId);
        try {
            if (jsonNode.has("data") && jsonNode.get("data").has("asr_paths")) {
                JsonNode asrPathsNode = jsonNode.get("data").get("asr_paths");

                // 遍历ASR路径数据
                asrPathsNode.fields().forEachRemaining(entry -> {
                    String videoId = entry.getKey();
                    String asrPath = entry.getValue().asText();

                    log.info("更新视频ASR路径: videoId={}, asrPath={}", videoId, asrPath);

                    try {
                        // 获取视频并更新ASR路径
                        Optional<Video> videoOptional = videoRepository.findById(Long.parseLong(videoId));
                        if (videoOptional.isPresent()) {
                            Video video = videoOptional.get();
                            video.setAsrPath(asrPath);
                            videoRepository.save(video);
                            log.info("成功更新视频ASR路径: videoId={}", videoId);
                        } else {
                            log.error("找不到视频: videoId={}", videoId);
                        }
                    } catch (NumberFormatException e) {
                        log.error("无效的视频ID格式: {}", videoId, e);
                    } catch (Exception e) {
                        log.error("更新视频ASR路径时出错: videoId={}", videoId, e);
                    }
                });
            } else {
                log.error("ASR完成事件缺少必要的数据字段: {}", jsonNode);
            }
        } catch (Exception e) {
            log.error("处理ASR完成事件时出错: {}", e.getMessage(), e);
        }
    }

    private void completeDramaWithFilesWorkflow(String workflowInstanceId, String messageBody) {
        try {
            JsonNode root = JsonUtils.getInstance().readTree(messageBody);
            JsonNode dataNode = root.get("data");
            if (dataNode == null) {
                log.error("drama_with_files_completed事件缺少data字段: {}", messageBody);
                workflowService.updateWorkflowInstanceStatus(workflowInstanceId, com.sparkview.common.WorkflowInstanceStatus.SUCCESS);
                return;
            }
            
            // 1. 完整复用completeDramaWorkflow的逻辑处理merge_result
            JsonNode mergeResultNode = dataNode.get("merge_result");
            if (mergeResultNode == null) {
                log.error("drama_with_files_completed事件缺少merge_result字段: {}", messageBody);
                workflowService.updateWorkflowInstanceStatus(workflowInstanceId, com.sparkview.common.WorkflowInstanceStatus.SUCCESS);
                return;
            }
            
            // 处理剪辑记录
            List<ClipRecord> clipRecordInfos = null;
            if (mergeResultNode.has("pre_clip_list")) {
                List<List<DramaClioPreClipInfo>> preClipList = com.sparkview.common.JsonUtils.toObject(
                        mergeResultNode.get("pre_clip_list").toString(),
                        new com.fasterxml.jackson.core.type.TypeReference<List<List<DramaClioPreClipInfo>>>() {});
                clipRecordInfos = getClipRecordInfo(preClipList, workflowInstanceId);
                if (clipRecordInfos != null) {
                    clipRecordService.saveRecord(clipRecordInfos);
                }
            }
            
            // 处理原始视频结果
            List<DramaClipVideo> originalResult = new ArrayList<>();
            if (mergeResultNode.has("result")) {
                JsonNode resultNode = mergeResultNode.get("result");
                if (resultNode.isArray()) {
                    for (JsonNode videoNode : resultNode) {
                        DramaClipVideo video = JsonUtils.getInstance().treeToValue(videoNode, DramaClipVideo.class);
                        originalResult.add(video);
                    }
                }
            }
            
            // 2. 处理overlay_result，生成overlay视频
            List<VideoDto> allVideoList = new ArrayList<>();
            Map<String, String> overlayMapping = new HashMap<>();
            
            // 先添加所有原始视频
            for (DramaClipVideo video : originalResult) {
                VideoDto originalVideoDto = video.getMetadata();
                allVideoList.add(originalVideoDto);
                log.info("添加原始视频: {}", originalVideoDto.getObjectKey());
            }
            
            // 处理overlay视频
            JsonNode overlayResultNode = dataNode.get("overlay_result");
            if (overlayResultNode != null && overlayResultNode.has("processed_files")) {
                JsonNode processedFilesNode = overlayResultNode.get("processed_files");
                if (processedFilesNode.isArray()) {
                    for (JsonNode fileNode : processedFilesNode) {
                        String originalOssKey = fileNode.get("original_oss_key").asText();
                        String overlayOssKey = fileNode.get("overlay_oss_key").asText();
                        String overlayFilename = fileNode.get("overlay_filename").asText();
                        
                        // 找到对应的原始视频，复用其元数据创建overlay视频
                        for (DramaClipVideo video : originalResult) {
                            VideoDto originalVideoDto = video.getMetadata();
                            if (originalOssKey.equals(originalVideoDto.getObjectKey())) {
                                // 创建overlay视频，复用原始视频的元数据
                                VideoDto overlayVideoDto = new VideoDto();
                                overlayVideoDto.setObjectKey(overlayOssKey);
                                overlayVideoDto.setCoverKey(originalVideoDto.getCoverKey());
                                overlayVideoDto.setAspectRatio(originalVideoDto.getAspectRatio());
                                overlayVideoDto.setDuration(originalVideoDto.getDuration());
                                overlayVideoDto.setSize(originalVideoDto.getSize());
                                overlayVideoDto.setSubtitle(originalVideoDto.getSubtitle());
                                
                                allVideoList.add(overlayVideoDto);
                                overlayMapping.put(originalOssKey, overlayOssKey);
                                log.info("添加overlay视频: {} -> {}", originalOssKey, overlayOssKey);
                                break;
                            }
                        }
                    }
                }
            }
            
            // 3. 处理错误信息
            String errorStr = null;
            if (overlayResultNode != null && overlayResultNode.has("errors")) {
                JsonNode errorsNode = overlayResultNode.get("errors");
                if (errorsNode.isArray() && errorsNode.size() > 0) {
                    List<String> errorList = new ArrayList<>();
                    for (JsonNode err : errorsNode) {
                        errorList.add(err.asText());
                    }
                    errorStr = String.join("\n", errorList);
                }
            }
            
            // 4. 保存到数据库
            String outputJson = JsonUtils.toJson(allVideoList);
            Optional<com.sparkview.persistence.WorkflowInstance> optional = workflowService.getWorkflowInstanceById(workflowInstanceId);
            if (optional.isPresent()) {
                com.sparkview.persistence.WorkflowInstance instance = optional.get();
                instance.setStatus(com.sparkview.common.WorkflowInstanceStatus.SUCCESS);
                if (outputJson != null) {
                    List<com.sparkview.persistence.VideoFile> videoFiles = com.sparkview.common.JsonUtils.toObject(outputJson, new com.fasterxml.jackson.core.type.TypeReference<List<com.sparkview.persistence.VideoFile>>() {});
                    instance.setOutput(videoFiles);
                }
                instance.setVideoCount(originalResult.size()); // 视频数量以原始视频为准
                instance.setSubtitles(null); // 保持与completeDramaWorkflow一致
                if (errorStr != null) {
                    instance.setError(errorStr);
                }
                workflowService.saveWorkflowInstance(instance);
                log.info("短剧贴片工作流完成，保存了{}个视频（{}个原始+{}个overlay）", 
                    allVideoList.size(), originalResult.size(), overlayMapping.size());
            } else {
                log.error("未找到workflowInstance: {}", workflowInstanceId);
            }
        } catch (Exception e) {
            log.error("处理drama_with_files_completed事件异常: {}", e.getMessage(), e);
            workflowService.updateWorkflowInstanceStatus(workflowInstanceId, com.sparkview.common.WorkflowInstanceStatus.SUCCESS);
        }
    }

    @Data
    public static class CompleteDramaMessage {
        private String event;
        @SerializedName("workflow_instance_id")
        private String workflowInstanceId;
        private DramaClipResult data;
    }

    @Data
    public static class DramaClipResult {
        @SerializedName("pre_clip_list")
        private List<List<DramaClioPreClipInfo>> preClipList;
        private List<DramaClipVideo> result;
    }

    @Data
    public static class DramaClioPreClipInfo {
        private String uid;
        @SerializedName("video_id")
        private String videoId;
        private String start;
        private String end;
    }

    @Data
    public static class DramaClipVideo {
        private String uid;
        @SerializedName("workflow_instance_id")
        private String workflowInstanceId;
        @SerializedName("clip_record_video_key")
        private String clipRecordVideoKey;
        private VideoDto metadata;
    }

    /**
     * {"event": "workflow_completed", "workflow_instance_id":
     * "456ca956-2114-43b6-97fe-8edd60f57027", "data": [{"objectKey":
     * "users/123456/workflow_instances/\u51bb\u5e72\u5496\u5561-\u65e0\u5b57\u5e55//Cocinare
     * Quickshot Instant Cold Brew Coffe--889.mp4", "coverKey":
     * "static/video_covers/4343831de07a5b073e6aa47b790974f5.jpg", "aspectRatio":
     * "9:16", "duration": "22.88", "size": 7898850}, {"objectKey":
     * "users/123456/workflow_instances/\u51bb\u5e72\u5496\u5561-\u65e0\u5b57\u5e55//Cocinare
     * Quickshot Instant Cold Brew Coffe--890.mp4", "coverKey":
     * "static/video_covers/794a32ebd1ef22267a31ea26d5be35f4.jpg", "aspectRatio":
     * "9:16", "duration": "22.40", "size": 6753341}, {"objectKey":
     * "users/123456/workflow_instances/\u51bb\u5e72\u5496\u5561-\u65e0\u5b57\u5e55//Cocinare
     * Quickshot Instant Cold Brew Coffe--891.mp4", "coverKey":
     * "static/video_covers/1fb0f4854c65ed51cec49848d7426003.jpg", "aspectRatio":
     * "9:16", "duration": "26.36", "size": 7613842}]}
     */
    @Data
    public static class CompleteMessage {
        private String event;
        private String workflowInstanceId;
        private List<VideoDto> data;
    }

    @Data
    public static class VideoDto {
        private String objectKey;
        private String coverKey;
        private String aspectRatio;
        private String duration;
        private long size;
        private String subtitle;
    }

    /**
     * 启动定期清理retry记录的任务
     */
    private void startRetryMapCleanupTask() {
        executorService.submit(() -> {
            while (running.get()) {
                try {
                    // 每小时执行一次清理
                    Thread.sleep(60 * 60 * 1000);
                    cleanupOldRetryRecords();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    log.error("Error during retry map cleanup task", e);
                }
            }
        });
        log.info("Started retry map cleanup task");
    }

    /**
     * 清理过期的retry记录
     */
    private void cleanupOldRetryRecords() {
        long currentTime = System.currentTimeMillis();
        int cleaned = 0;

        for (Iterator<Map.Entry<String, Long>> it = retryTimestampMap.entrySet().iterator(); it.hasNext();) {
            Map.Entry<String, Long> entry = it.next();
            if (currentTime - entry.getValue() > RETRY_RECORD_TTL_MS) {
                String workflowInstanceId = entry.getKey();
                retryCountMap.remove(workflowInstanceId);
                it.remove();
                cleaned++;
            }
        }

        if (cleaned > 0) {
            log.info("Cleaned up {} expired retry records, current size: {}", cleaned, retryCountMap.size());
        }
    }

    /**
     * 启动线程池监控任务
     */
    private void startThreadPoolMonitorTask() {
        Thread monitorThread = new Thread(() -> {
            while (running.get()) {
                try {
                    // 每分钟监控一次
                    Thread.sleep(60 * 1000);

                    if (executorService instanceof java.util.concurrent.ThreadPoolExecutor) {
                        java.util.concurrent.ThreadPoolExecutor executor =
                                (java.util.concurrent.ThreadPoolExecutor) executorService;

                        int activeCount = executor.getActiveCount();
                        long completedTaskCount = executor.getCompletedTaskCount();
                        int poolSize = executor.getPoolSize();
                        int queueSize = executor.getQueue().size();

                        log.info("Thread pool stats - Active: {}, Completed: {}, Pool Size: {}, Queue Size: {}",
                                activeCount, completedTaskCount, poolSize, queueSize);

                        // 如果队列过长，发出警告
                        if (queueSize > 100) {
                            log.warn("Thread pool queue is large ({}), consider increasing thread pool size", queueSize);
                            // 可以在这里添加报警逻辑
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    log.error("Error in thread pool monitor task", e);
                }
            }
        });
        monitorThread.setDaemon(true);
        monitorThread.setName("thread-pool-monitor");
        monitorThread.start();
        log.info("Started thread pool monitor task");
    }
}

