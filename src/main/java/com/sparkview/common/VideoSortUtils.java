package com.sparkview.common;

import com.google.common.collect.Lists;
import com.sparkview.controller.dto.VideoDto;
import com.sparkview.controller.dto.VideoInfoDto;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: gavingeng
 * @create: 2025-05-14 16:02
 * @description:
 **/
public class VideoSortUtils {

    /**
     * 根据object_key中的数字部分对VideoDto列表进行排序
     * 例如：从"正义必胜-20.mp4"中提取数字20进行排序
     * 当存在相同数字但不同后缀的文件时，优先保留mp4格式的文件
     *
     * @param videoDtoList 需要排序的VideoDto列表
     * @return 排序后的新VideoDto列表
     */
    public static List<VideoDto> sortByObjectKeyNumber(List<VideoDto> videoDtoList) {
        if (videoDtoList == null || videoDtoList.isEmpty()) {
            return new ArrayList<>();
        }

        List<VideoDto> result = Lists.newArrayList(videoDtoList);

        // 定义正则表达式，匹配文件名中的数字部分
        Pattern pattern = Pattern.compile("-(\\d+)\\.[^.]+$");

        // 用于存储相同数字的不同文件，key为数字，value为对应的VideoDto列表
        java.util.Map<Integer, List<VideoDto>> numberMap = new java.util.HashMap<>();

        // 对视频按数字分组
        for (VideoDto video : result) {
            if (video == null || video.getObject_key() == null) continue;

            String key = video.getObject_key();
            String fileName = key.substring(key.lastIndexOf('/') + 1);
            Matcher matcher = pattern.matcher(fileName);

            if (matcher.find()) {
                Integer num = Integer.parseInt(matcher.group(1));
                numberMap.computeIfAbsent(num, k -> new ArrayList<>()).add(video);
            }
        }

        // 筛选每组中优先级最高的文件
        List<VideoDto> filteredList = new ArrayList<>();
        for (List<VideoDto> videos : numberMap.values()) {
            if (videos.size() == 1) {
                filteredList.add(videos.get(0));
            } else {
                // 优先选择mp4格式
                VideoDto selected = null;
                for (VideoDto video : videos) {
                    String key = video.getObject_key().toLowerCase();
                    if (key.endsWith(".mp4")) {
                        selected = video;
                        break;
                    }
                }

                // 如果没有mp4格式，选择第一个
                if (selected == null) {
                    selected = videos.get(0);
                }

                filteredList.add(selected);
            }
        }

        // 按数字排序
        filteredList.sort((v1, v2) -> {
            if (v1 == null || v1.getObject_key() == null) return -1;
            if (v2 == null || v2.getObject_key() == null) return 1;

            String key1 = v1.getObject_key();
            String key2 = v2.getObject_key();

            String fileName1 = key1.substring(key1.lastIndexOf('/') + 1);
            String fileName2 = key2.substring(key2.lastIndexOf('/') + 1);

            Matcher matcher1 = pattern.matcher(fileName1);
            Matcher matcher2 = pattern.matcher(fileName2);

            Integer num1 = matcher1.find() ? Integer.parseInt(matcher1.group(1)) : Integer.MAX_VALUE;
            Integer num2 = matcher2.find() ? Integer.parseInt(matcher2.group(1)) : Integer.MAX_VALUE;

            return num1.compareTo(num2);
        });

        return filteredList;
    }

    /**
     * 用于前端排序
     *
     * @param videoList
     * @return
     */
    public static List<VideoInfoDto> sortByObjectKeyNo(List<VideoInfoDto> videoList) {
        if (videoList == null || videoList.isEmpty()) {
            return new ArrayList<>();
        }

        List<VideoInfoDto> result = Lists.newArrayList(videoList);

        // 定义正则表达式，匹配文件名中的数字部分
        Pattern pattern = Pattern.compile("-(\\d+)\\.[^.]+$");

        result.sort((v1, v2) -> {
            if (v1 == null || v1.getObjectKey() == null) return -1;
            if (v2 == null || v2.getObjectKey() == null) return 1;

            String key1 = v1.getObjectKey();
            String key2 = v2.getObjectKey();

            // 从路径中提取文件名
            String fileName1 = key1.substring(key1.lastIndexOf('/') + 1);
            String fileName2 = key2.substring(key2.lastIndexOf('/') + 1);

            // 匹配文件名中的数字部分
            Matcher matcher1 = pattern.matcher(fileName1);
            Matcher matcher2 = pattern.matcher(fileName2);

            Integer num1 = matcher1.find() ? Integer.parseInt(matcher1.group(1)) : Integer.MAX_VALUE;
            Integer num2 = matcher2.find() ? Integer.parseInt(matcher2.group(1)) : Integer.MAX_VALUE;

            return num1.compareTo(num2);
        });

        return result;
    }

    public static void main(String[] args) {
        List<VideoDto> videoDtoList = new ArrayList<>();
        VideoDto videoDto = VideoDto.builder()
                .object_key("test/test-10.mp4")
                .build();
        videoDtoList.add(videoDto);
        videoDto = VideoDto.builder()
                .object_key("test/test-10.mov")
                .build();
        videoDtoList.add(videoDto);

        videoDto = VideoDto.builder()
                .object_key("test/test-2.mp4")
                .build();
        videoDtoList.add(videoDto);

        videoDto = VideoDto.builder()
                .object_key("test/test-11.mp4")
                .build();
        videoDtoList.add(videoDto);

        List<VideoDto> result = sortByObjectKeyNumber(videoDtoList);
        for (VideoDto v : result){
            System.out.println(v.getObject_key());
        }

    }

}
