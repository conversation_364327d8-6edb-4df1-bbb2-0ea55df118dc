package com.sparkview.common;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: gavingeng
 * @create: 2025-05-22 01:20
 * @description:
 **/
@Slf4j
@Service
public class FeishuUtil {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private Environment env;

    private static final String feishuWebhookUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/2553972c-7cd7-44e6-9707-73312d88c860";
    private static final String feishuSecret = "rCBYXx9UbIqmcs5gR7nvPg";

    public void sendFeishuAlert(String workflowInstanceId, int retryCount) {
        try {
            if (StringUtils.isEmpty(feishuWebhookUrl)) {
                log.error("Feishu webhook URL is not configured, cannot send alert");
                return;
            }

            long timestamp = System.currentTimeMillis()/1000;
            String sign = generateSign(feishuSecret, timestamp);

            Map<String, Object> payload = new HashMap<>();
            payload.put("msg_type", "post");

            Map<String, Object> content = new HashMap<>();
            Map<String, Object> post = new HashMap<>();
            Map<String, Object> zhCn = new HashMap<>();

            zhCn.put("title", "🚨 Workflow Retry Alert 🚨");

            List<List<Map<String, String>>> contentList = new ArrayList<>();

            // Add content items
            addContentItem(contentList, "ENV", String.join(",", env.getActiveProfiles()));
            addContentItem(contentList, "Workflow Instance ID", workflowInstanceId);
            addContentItem(contentList, "Retry Count", String.valueOf(retryCount));
            addContentItem(contentList, "Max Retry Limit", "3");
            addContentItem(contentList, "Timestamp", String.valueOf(timestamp));

            zhCn.put("content", contentList);
            post.put("zh_cn", zhCn);
            content.put("post", post);
            payload.put("content", content);

            if (StringUtils.isNotBlank(feishuSecret)) {
                payload.put("timestamp", timestamp);
                payload.put("sign", sign);
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(payload, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(feishuWebhookUrl, request, String.class);
            String responseBody = response.getBody();

            if (response.getStatusCode().is2xxSuccessful()) {
                if (StringUtils.isNotBlank(responseBody)) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    Map<String, Object> responseMap = objectMapper.readValue(responseBody, new TypeReference<Map<String, Object>>() {});

                    Integer feishuCode = null;
                    if (responseMap.containsKey("StatusCode") && responseMap.get("StatusCode") instanceof Integer) {
                        feishuCode = (Integer) responseMap.get("StatusCode");
                    } else if (responseMap.containsKey("code") && responseMap.get("code") instanceof Integer) {
                        feishuCode = (Integer) responseMap.get("code");
                    }

                    if (feishuCode != null && feishuCode == 0) {
                        log.info("Successfully sent Feishu alert for workflow instance: {}. Feishu response: {}", workflowInstanceId, responseBody);
                    } else {
                        log.error("Feishu alert API call succeeded (HTTP 2xx) but reported a business error for workflow instance: {}. HTTP Status: {}, Feishu Code: {}, Response Body: {}",
                                workflowInstanceId, response.getStatusCodeValue(), feishuCode, responseBody);
                    }
                } else {
                    log.warn("Feishu alert HTTP call was successful (2xx) but returned an empty body for workflow instance: {}. HTTP Status: {}",
                            workflowInstanceId, response.getStatusCodeValue());
                }
            } else {
                log.error("Failed to send Feishu alert due to HTTP error for workflow instance: {}. HTTP Status: {}, Response Body: {}",
                        workflowInstanceId, response.getStatusCodeValue(), responseBody);
            }
        } catch (Exception e) {
            log.error("Error sending Feishu alert for workflow instance: {}", workflowInstanceId, e);
        }
    }

    private static String generateSign(String secret, long timestamp) throws NoSuchAlgorithmException, InvalidKeyException {
        //把timestamp+"\n"+密钥当做签名字符串
        String stringToSign = timestamp + "\n" + secret;
        //使用HmacSHA256算法计算签名
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(new byte[]{});
        return new String(Base64.encodeBase64(signData));
    }
    private void addContentItem(List<List<Map<String, String>>> contentList, String label, String value) {
        List<Map<String, String>> item = new ArrayList<>();

        Map<String, String> labelMap = new HashMap<>();
        labelMap.put("tag", "text");
        labelMap.put("text", label + ": ");

        Map<String, String> valueMap = new HashMap<>();
        valueMap.put("tag", "text");
        valueMap.put("text", value);

        item.add(labelMap);
        item.add(valueMap);

        contentList.add(item);
    }
}
