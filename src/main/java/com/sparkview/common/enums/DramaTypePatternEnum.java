package com.sparkview.common.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sparkview.common.RequestContext;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: gaving<PERSON>
 * @create: 2025-04-23 11:39
 * @description: 剧目类型枚举，用于标记不同类型的剧目
 **/
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DramaTypePatternEnum {
    TORTURED_ROMANCE(1, new HashMap<Lang, String>() {
        {
            put(Lang.EN_<PERSON>, "Tortured Romance");
            put(Lang.ZH_CN, "爱情虐恋");
        }
    }, new HashMap<Lang, List<String>>() {{
        put(Lang.EN_US, Arrays.asList("Bitter Love", "Love-hate", "Toix Relationship", "Misunderstanding", "Forbidden Love"));
        put(<PERSON>.<PERSON>H_CN, Arrays.asList("苦情恋", "爱恨交织", "有毒的关系", "误解", "禁忌之恋"));
    }}),
    MELODRAMATIC_ETHICS(2, new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "Melodramatic Ethics");
            put(Lang.ZH_CN, "狗血伦理");
        }
    }, new HashMap<Lang, List<String>>() {{
        put(Lang.EN_US, Arrays.asList("Drama", "Betrayal", "Scandal", "Family Drama", "The Real and Fake Heiress"));
        put(Lang.ZH_CN, Arrays.asList("抓马", "背叛", "丑闻", "家庭伦理", "真假千金"));
    }}),
    FACE_SLAPPING(3, new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "Face-Slapping Reversals");
            put(Lang.ZH_CN, "打脸反转");
        }
    }, new HashMap<Lang, List<String>>() {{
        put(Lang.EN_US, Arrays.asList("Hidden identity", "Face-Slapping", "PlotTwist", "Divine Tycoon", "Comeback"));
        put(Lang.ZH_CN, Arrays.asList("隐藏身份", "打脸", "反转", "神豪", "逆袭"));
    }}),
    REVENGE_REBIRTH(4, new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "Revenge & Rebirth");
            put(Lang.ZH_CN, "复仇重生");
        }
    }, new HashMap<Lang, List<String>>() {{
        put(Lang.EN_US, Arrays.asList("Revenge", "Rebirth", "Hatred", "Mystery", "Counterattack"));
        put(Lang.ZH_CN, Arrays.asList("复仇", "重生", "仇恨", "神秘", "反击"));
    }}),
    SWEET_ROMANCE(5, new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "Romance & Sweet Love");
            put(Lang.ZH_CN, "爱情甜宠");
        }
    }, new HashMap<Lang, List<String>>() {{
        put(Lang.EN_US, Arrays.asList("Romance", "Sweet Love", "Billionaire", "Contract Marriage", "Marriage Before Love"));
        put(Lang.ZH_CN, Arrays.asList("浪漫", "甜蜜爱情", "亿万富翁", "契约婚姻", "先婚后爱"));
    }}),
    MYSTERY_CRIME(6, new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "Mystery & Crime");
            put(Lang.ZH_CN, "悬疑犯罪");
        }
    }, new HashMap<Lang, List<String>>() {{
        put(Lang.EN_US, Arrays.asList("Suspense", "Mystery", "Thriller", "Crime", "Mafia"));
        put(Lang.ZH_CN, Arrays.asList("悬念", "神秘", "惊悚", "犯罪", "黑手党"));
    }}),
    FANTASY_SUPERNATURAL(7, new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "Fantasy & Supernatural");
            put(Lang.ZH_CN, "奇幻超自然");
        }
    }, new HashMap<Lang, List<String>>() {{
        put(Lang.EN_US, Arrays.asList("Supernatural", "Paranormal", "Super Power", "Fantasy", "Time travel"));
        put(Lang.ZH_CN, Arrays.asList("超自然", "灵异", "超能力", "奇幻", "穿越"));
    }}),
    SOCIAL_REALISM(8, new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "Social Realism");
            put(Lang.ZH_CN, "现实主义");
        }
    }, new HashMap<Lang, List<String>>() {{
        put(Lang.EN_US, Arrays.asList("Class Conflict", "Workplace", "Social Realism", "Independent Woman", "Redemption"));
        put(Lang.ZH_CN, Arrays.asList("阶级矛盾", "职场", "社会现实", "独立女性", "救赎"));
    }}),
    COMEDY(9, new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "Comedy");
            put(Lang.ZH_CN, "轻型喜剧");
        }
    }, new HashMap<Lang, List<String>>() {{
        put(Lang.EN_US, Arrays.asList("Comedy", "Humor"));
        put(Lang.ZH_CN, Arrays.asList("喜剧", "幽默"));
    }}),
    MALE_TARGETED(10, new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "Male-Targeted Dramas");
            put(Lang.ZH_CN, "男频主义");
        }
    }, new HashMap<Lang, List<String>>() {{
        put(Lang.EN_US, Arrays.asList("Divine Tycoon", "Sudden Wealth", "Son-in-law", "Fantasy-male", "Harem"));
        put(Lang.ZH_CN, Arrays.asList("神豪", "暴富", "女婿", "男性幻想", "后宫"));
    }});

    @Getter
    private final int value;
    private final Map<Lang, String> labelMap;
    private final Map<Lang, List<String>> subLabelsMap;

    DramaTypePatternEnum(int value, Map<Lang, String> labelMap, Map<Lang, List<String>> subLabelsMap) {
        this.value = value;
        this.labelMap = labelMap;
        this.subLabelsMap = subLabelsMap;
    }

    public String getLabel() {
        // 获取系统默认语言环境
        Lang lang = Lang.fromCode(RequestContext.getCurrentLanguage());
        return getLabel(lang);
    }

    public String getLabel(Lang lang) {
        // 如果找不到对应语言的标签，则返回默认语言的标签
        return labelMap.getOrDefault(lang, labelMap.get(Lang.DEFAULT_LANG));
    }

    public List<String> getSubLabels() {
        Lang lang = Lang.fromCode(RequestContext.getCurrentLanguage());
        return getSubLabels(lang);
    }

    public List<String> getSubLabels(Lang lang) {
        return subLabelsMap.getOrDefault(lang, subLabelsMap.get(Lang.DEFAULT_LANG));
    }

    public static DramaTypePatternEnum fromValue(int value) {
        for (DramaTypePatternEnum pattern : DramaTypePatternEnum.values()) {
            if (pattern.getValue() == value) {
                return pattern;
            }
        }
        return null;
    }

    public static DramaTypePatternEnum fromLabel(String label) {
        for (DramaTypePatternEnum pattern : DramaTypePatternEnum.values()) {
            if (pattern.getLabel().equalsIgnoreCase(label)) {
                return pattern;
            }
        }
        return null;
    }

    public static List<DramaTypePatternEnum> allValues() {
        return Arrays.asList(DramaTypePatternEnum.values());
    }

    public static String getName(int value) {
        DramaTypePatternEnum type = fromValue(value);
        if (type == null) {
            return "unknown";
        }
        
        switch (type) {
            case TORTURED_ROMANCE:
                return "tortured_romance";
            case MELODRAMATIC_ETHICS:
                return "melodramatic_ethics";
            case FACE_SLAPPING:
                return "face_slapping";
            case REVENGE_REBIRTH:
                return "revenge_rebirth";
            case SWEET_ROMANCE:
                return "sweet_romance";
            case MYSTERY_CRIME:
                return "mystery_crime";
            case FANTASY_SUPERNATURAL:
                return "fantasy_supernatural";
            case SOCIAL_REALISM:
                return "social_realism";
            case COMEDY:
                return "comedy";
            case MALE_TARGETED:
                return "male_targeted";
            default:
                return "unknown";
        }
    }

    public static DramaTypePatternEnum fromString(String name) {
        if (name == null) {
            return SWEET_ROMANCE; // 默认返回SWEET_ROMANCE类型
        }
        
        switch (name.toLowerCase()) {
            case "tortured_romance":
                return TORTURED_ROMANCE;
            case "melodramatic_ethics":
                return MELODRAMATIC_ETHICS;
            case "face_slapping":
                return FACE_SLAPPING;
            case "revenge_rebirth":
                return REVENGE_REBIRTH;
            case "sweet_romance":
                return SWEET_ROMANCE;
            case "mystery_crime":
                return MYSTERY_CRIME;
            case "fantasy_supernatural":
                return FANTASY_SUPERNATURAL;
            case "social_realism":
                return SOCIAL_REALISM;
            case "comedy":
                return COMEDY;
            case "male_targeted":
                return MALE_TARGETED;
            default:
                return SWEET_ROMANCE; // 默认返回SWEET_ROMANCE类型
        }
    }
}
