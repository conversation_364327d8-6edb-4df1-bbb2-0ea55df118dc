package com.sparkview.common.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sparkview.common.RequestContext;

import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum FolderTypeEnum {
    E_COMMERCE(0, "e_commerce", new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "E-Commerce");
            put(Lang.ZH_CN, "电子商务");
            // Can add more languages here
        }
    }, ""),

    SHORT_DRAMA(1, "short_drama", new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "Short Drama");
            put(Lang.ZH_CN, "短剧");
            // Can add more languages here
        }
    }, "");

    @Getter
    private final int value;
    @Getter
    private final String name;
    private final Map<Lang, String> labelMap;
    @Getter
    private final String description;

    FolderTypeEnum(int value, String name, Map<Lang, String> labelMap, String description) {
        this.value = value;
        this.name = name;
        this.labelMap = labelMap;
        this.description = description;
    }

    public String getLabel() {
        // Get current language from system context
        Lang lang = Lang.fromCode(RequestContext.getCurrentLanguage());
        return getLabel(lang);
    }

    public String getLabel(Lang lang) {
        // If label not found for the requested language, return default language label
        return labelMap.getOrDefault(lang, labelMap.get(Lang.DEFAULT_LANG));
    }

    public static FolderTypeEnum fromName(String name) {
        for (FolderTypeEnum type : FolderTypeEnum.values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static List<FolderTypeEnum> allValues() {
        return Arrays.asList(FolderTypeEnum.values());
    }
}