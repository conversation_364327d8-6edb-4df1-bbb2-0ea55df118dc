package com.sparkview.persistence;

import jakarta.persistence.*;
import lombok.Data;

/**
 * @author: gavingeng
 * @create: 2025-04-17 10:41
 * @description:
 **/
@Data
@Entity
@Table(name = "clip_record")
public class ClipRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "uid", nullable = false)
    private String uid;

    @Column(name = "start_video_id")
    private Long startVideoId;

    @Column(name = "start_time")
    private String startTime;

    @Column(name = "end_video_id")
    private Long endVideoId;

    @Column(name = "end_time")
    private String endTime;

    @Column(name = "created_at")
    private long createdAt;

    @PrePersist
    protected void onCreate() {
        this.createdAt = System.currentTimeMillis();
    }

}

