package com.sparkview.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.sparkview.common.JsonUtils;
import com.sparkview.persistence.Effects;
import com.sparkview.repository.EffectsDao;
import com.sparkview.repository.HighlightRepository;
import com.sparkview.repository.VideoRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: gavingeng
 * @create: 2025-04-20 17:32
 * @description:
 **/
@Service
@Slf4j
public class EffectsService {

    @Resource
    private HighlightRepository highlightRepository;

    @Resource
    private VideoRepository videoRepository;

    @Resource
    private EffectsDao effectsDao;

    @Transactional
    public void updateEffects(String data) {
        try {
            JsonNode jsonNodeArray = JsonUtils.getInstance().readTree(data);
            if (jsonNodeArray.isArray()) {
                List<Effects> effects = Lists.newArrayList();
                for (JsonNode jsonNode : jsonNodeArray) {
                    long videoId = jsonNode.get("video_id").asLong();
                    String uid = jsonNode.get("uid").asText();
                    String dataStr = jsonNode.get("data").asText();
                    Effects effect = Effects.builder()
                            .videoId(videoId)
                            .uid(uid)
                            .data(dataStr)
                            .status(0)
                            .createdAt(System.currentTimeMillis())
                            .build();
                    effects.add(effect);
                }
                if (!effects.isEmpty()) {
                    log.info("effects: {}", JsonUtils.toJson(effects));
                    effectsDao.batchUpsert(effects);
                }
            }
        } catch (Exception e) {
            log.error("error", e);
            e.printStackTrace();
        }
    }
}
