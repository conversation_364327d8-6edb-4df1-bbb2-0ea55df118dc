package com.sparkview.service;

import com.sparkview.common.ConfigItem;

import java.util.Arrays;
import java.util.List;

import com.sparkview.common.enums.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.sparkview.common.ConfigTemplate;

@Service
public class ConfigTemplateService {
    @Value("${aws.s3.domain}")
    private String s3Domain;

    public ConfigTemplate getEcommerceTemplate() {
        return new ConfigTemplate(
                // 初始化配置项
                Arrays.asList(
                        new ConfigItem("videoList", ConfigItem.createLabelMap("Video List", "视频列表"),
                                "list", null,
                                ""),
                        new ConfigItem("name", ConfigItem.createLabelMap("Product Name", "产品名称"),
                                "text", null, ""),
                        new ConfigItem("ecommerceCategory", ConfigItem.createLabelMap("Category", "类别"), "enum",
                                CategoryEnum.allValues(), ""),
                        new ConfigItem("sellingPoints",
                                ConfigItem.createLabelMap("Selling Points", "卖点"), "text", null,
                                ""),
                        new ConfigItem("textStyle", ConfigItem.createLabelMap("Text Style", "文本样式"),
                                "text",
                                TextStyleEnum.allValues(), ""),
                        new ConfigItem("textSize", ConfigItem.createLabelMap("Text Size", "文本大小"),
                                "text", null, "36"),
                        new ConfigItem("boldType", ConfigItem.createLabelMap("Bold Type", "加粗"),
                                "enum",
                                BoldTypeEnum.allValues(), ""),
                        new ConfigItem("textPosition",
                                ConfigItem.createLabelMap("Text Position", "字幕"), "enum",
                                TextPositionEnum.allValues(), null),
                        new ConfigItem("vfx", ConfigItem.createLabelMap("VFX", "视觉特效"), "enum",
                                VfxEnum.allValues(), null),
                        new ConfigItem("textColor", ConfigItem.createLabelMap("Text Color", "字体颜色"),
                                "color",
                                ColorEnum.allValues(), null),
                        new ConfigItem("vfxColor",
                                ConfigItem.createLabelMap("Special Effect Color", "特效颜色"),
                                "color",
                                ColorEnum.allValues(), null),
                        new ConfigItem("textStrokeColor",
                                ConfigItem.createLabelMap("Text Stroke Color", "文本描边颜色"),
                                "color",
                                ColorEnum.allValues(), null),
                        new ConfigItem("aspectRatio", ConfigItem.createLabelMap("Aspect Ratio", "宽高比"),
                                "enum",
                                AspectRatioEnum.allValues(), null),
                        new ConfigItem("tts", ConfigItem.createLabelMap("TTS", "文本转语音"), "tts",
                                TtsEnum.allValuesWithDomain(""), null),
                        new ConfigItem("withTextOver",
                                ConfigItem.createLabelMap("With TextOver", "是否显示文本"), "enum",
                                TextOverEnum.allValues(), null),
                        new ConfigItem("bgmList",
                                ConfigItem.createLabelMap("bgmList", "bgmList"), "list",
                                TextOverEnum.allValues(), null)
                ));
    }

    public ConfigTemplate getDramTemplate() {
        return new ConfigTemplate(
                // 初始化配置项
                Arrays.asList(
                        new ConfigItem("name", ConfigItem.createLabelMap("Name", "名称"), "text", null,
                                ""),
                        new ConfigItem("videoList", ConfigItem.createLabelMap("Video List", "视频列表"),
                                "list", null,
                                ""),
                        new ConfigItem("videoPattern",
                                ConfigItem.createLabelMap("Video Pattern", "视频模式"), "enum",
                                ShortDramaVideoPatternEnum.allValues(), null),
                        new ConfigItem("textStyle", ConfigItem.createLabelMap("Text Style", "文本样式"),
                                "text",
                                TextStyleEnum.allValues(), ""),
                        new ConfigItem("textSize", ConfigItem.createLabelMap("Text Size", "文本大小"),
                                "text", null, "36"),
                        new ConfigItem("boldType", ConfigItem.createLabelMap("Bold Type", "粗体类型"),
                                "enum",
                                BoldTypeEnum.allValues(),
                                ""),
                        new ConfigItem("textPosition",
                                ConfigItem.createLabelMap("Text Position", "文本位置"), "enum",
                                TextPositionEnum.allValues(), null),
                        new ConfigItem("vfx", ConfigItem.createLabelMap("VFX", "视觉特效"), "enum",
                                VfxEnum.allValues(), null),
                        new ConfigItem("textColor", ConfigItem.createLabelMap("Text Color", "字体颜色"),
                                "color",
                                ColorEnum.allValues(),
                                null),
                        new ConfigItem("vfxColor",
                                ConfigItem.createLabelMap("Special Effect Color", "特效颜色"),
                                "color",
                                ColorEnum.allValues(), null),
                        new ConfigItem("textStrokeColor",
                                ConfigItem.createLabelMap("Text Stroke Color", "文本描边颜色"),
                                "color",
                                ColorEnum.allValues(), null),
                        new ConfigItem("aspectRatio", ConfigItem.createLabelMap("Aspect Ratio", "宽高比"),
                                "enum",
                                AspectRatioEnum.allDramaValues(), null),
                        new ConfigItem("tts", ConfigItem.createLabelMap("TTS", "文本转语音"), "tts",
                                TtsEnum.allValuesWithDomain(s3Domain), null),
                        new ConfigItem("withTextOver",
                                ConfigItem.createLabelMap("With TextOver", "是否显示文本"), "enum",
                                TextOverEnum.allValues(), null),
                        new ConfigItem("lang",
                                ConfigItem.createLabelMap("Lines Language", "Lines Language"), "enum",
                                LangPatternEnum.allValues(), null),
                        new ConfigItem("videoDuration",
                                ConfigItem.createLabelMap("Video Duration", "视频时长"), "enum",
                                VideoDurationPatternEnum.allValues(), null),
                        new ConfigItem("dramaType",
                                ConfigItem.createLabelMap("Type Selection", "Type Selection"), "enum",
                                DramaTypePatternEnum.allValues(), null),
                        // 投流视频相关配置 - 画面贴片配置项
                        new ConfigItem("overlayModules", 
                                ConfigItem.createLabelMap("Overlay Modules", "贴片模块"), 
                                "multiselect",
                                Arrays.asList(
                                    "positionA", 
                                    "positionB", 
                                    "bottomText"
                                ), null),
                        // A位置贴片图片
                        new ConfigItem("positionAImage", 
                                ConfigItem.createLabelMap("Position A Image", "A位置图片"), 
                                "image",
                                List.of("png"), null),
                        // B位置贴片图片
                        new ConfigItem("positionBImage", 
                                ConfigItem.createLabelMap("Position B Image", "B位置图片"), 
                                "image",
                                List.of("png"), null),
                        // 底部文本
                        new ConfigItem("bottomText", 
                                ConfigItem.createLabelMap("Bottom Text", "底部文本"), 
                                "text", null, ""),
                        // 结尾视频
                        new ConfigItem("endingVideo", 
                                ConfigItem.createLabelMap("Ending Video", "结尾视频"), 
                                "video", null, null)
                ));
    }

}
