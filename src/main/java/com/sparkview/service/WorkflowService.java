package com.sparkview.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sparkview.common.JsonUtils;
import com.sparkview.common.WorkflowInstanceStatus;
import com.sparkview.common.enums.FolderTypeEnum;
import com.sparkview.common.enums.WorkflowSourceEnum;
import com.sparkview.persistence.FileCounter;
import com.sparkview.persistence.VideoFile;
import com.sparkview.persistence.WorkflowInstance;
import com.sparkview.repository.FileCounterRepository;
import com.sparkview.repository.WorkflowInstanceRepository;

import io.micrometer.common.util.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import javax.annotation.Nonnull;

@Slf4j
@Service
public class WorkflowService {

    @Value("${workflow.output-prefix}")
    private String workflowOutputPrefix;

    @Autowired
    private WorkflowInstanceRepository workflowInstanceRepository;
    @Autowired
    private FileCounterRepository fileCounterRepository;

    public List<WorkflowInstance> getWorkflowInstancesByUidWithFilters(String uid,
                                                                       WorkflowInstanceStatus status,
                                                                       FolderTypeEnum type) {
        if (status != null && type != null) {
            return workflowInstanceRepository.findByUidAndStatusAndTypeOrderByCreateTimeDesc(uid, status, type);
        } else if (status != null) {
            return workflowInstanceRepository.findByUidAndStatusOrderByCreateTimeDesc(uid, status);
        } else if (type != null) {
            return workflowInstanceRepository.findByUidAndTypeOrderByCreateTimeDesc(uid, type);
        } else {
            return workflowInstanceRepository.findByUidOrderByCreateTimeDesc(uid);
        }
    }

    /**
     * 分页获取用户已完成的工作流实例，按时间倒序排列
     *
     * @param uid  用户ID
     * @param page 页码，从0开始
     * @param size 每页大小
     * @return 已完成的工作流实例列表
     */
    public List<WorkflowInstance> getCompletedWorkflowsByUidAndStatusPaginated(String uid,
                                                                               WorkflowInstanceStatus status, int page, int size) {
        return workflowInstanceRepository.findCompletedByUidAndStatusOrderByCreateTimeDesc(uid, status,
                PageRequest.of(page, size));
    }

    public List<VideoFile> getDirFiles(String uid, FolderTypeEnum type, String dirName, int page, int size) {
        List<VideoFile> res = new ArrayList<>();
        // 获取该目录下所有成功的工作流实例
        List<WorkflowInstance> instances = workflowInstanceRepository
                .findByUidAndOutputDirAndStatusAndTypeOrderByCreateTimeDesc(
                        uid,
                        dirName,
                        WorkflowInstanceStatus.SUCCESS,
                        type);
        // 遍历工作流实例，获取输出文件并添加到结果列表中
        for (WorkflowInstance instance : instances) {
            if (instance.getOutput() != null && !instance.getOutput().isEmpty()) {
                // 假设输出是文件路径或包含文件信息的字符串
                if (CollectionUtils.isNotEmpty(instance.getOutput())) {
                    res.addAll(instance.getOutput());
                }
            }
        }
        return res;
    }

    public List<WorkflowInstance> getWorkflowInstancesByUidWithFiltersPaginated(String uid,
                                                                                WorkflowInstanceStatus status,
                                                                                FolderTypeEnum type,
                                                                                int page,
                                                                                int size) {
        PageRequest pageRequest = PageRequest.of(page, size);
        return workflowInstanceRepository.findWithFiltersExcludeSourceOne(uid, status, type, pageRequest);
    }

    /**
     * 根据状态获取指定数量的工作流实例
     *
     * @param status 工作流实例状态
     * @param limit  返回结果的最大数量
     * @return 符合指定状态的工作流实例列表
     */
    public List<WorkflowInstance> getWorkflowInstancesByStatus(WorkflowInstanceStatus status, int limit) {
        return workflowInstanceRepository.findByStatusOrderByCreateTimeDesc(status, PageRequest.of(0, limit));
    }

    /**
     * 保存工作流实例
     */
    public WorkflowInstance saveWorkflowInstance(WorkflowInstance instance) {
        return workflowInstanceRepository.save(instance);
    }

    /**
     * 根据ID获取工作流实例
     */
    public Optional<WorkflowInstance> getWorkflowInstanceById(String instanceId) {
        return workflowInstanceRepository.findByWorkflowInstanceId(instanceId);
    }

    /**
     * 获取用户的所有工作流实例
     */
    public List<WorkflowInstance> getWorkflowInstancesByUid(String uid) {
        return workflowInstanceRepository.findByUidOrderByCreateTimeDesc(uid);
    }

    public List<WorkflowInstance> getWorkflowInstancesByStatusAndType(WorkflowInstanceStatus status,
                                                                      FolderTypeEnum type, int limit) {
        return workflowInstanceRepository.findByStatusAndTypeOrderByCreateTimeDesc(status, type,
                PageRequest.of(0, limit));
    }

    /**
     * 更新工作流实例状态
     */
    public void updateWorkflowInstanceStatus(String instanceId, WorkflowInstanceStatus status, String output, String subtitles, int videoCount) {
        WorkflowInstance instance = workflowInstanceRepository.findByWorkflowInstanceId(instanceId)
                .orElseThrow(() -> new RuntimeException("工作流实例不存在"));

        instance.setStatus(status);
        int source = instance.getSource();
        if (output != null) {
            List<VideoFile> videoFiles = JsonUtils.toObject(output, new TypeReference<List<VideoFile>>() {
            });
            instance.setOutput(videoFiles);
        }
        if (subtitles != null) {
            instance.setSubtitles(subtitles);
        }

        instance.setVideoCount(videoCount);
        workflowInstanceRepository.save(instance);
    }

    public void updateHighlightWorkflowInstanceStatus(String instanceId, WorkflowInstanceStatus status, int videoCount) {
        WorkflowInstance instance = workflowInstanceRepository.findByWorkflowInstanceId(instanceId)
                .orElseThrow(() -> new RuntimeException("工作流实例不存在"));

        instance.setStatus(status);
        instance.setVideoCount(videoCount);
        workflowInstanceRepository.save(instance);
    }

    /**
     * 更新工作流subtitles
     */
    public void updateWorkflowSubtitles(String instanceId, String subtitles) {
        WorkflowInstance instance = workflowInstanceRepository.findByWorkflowInstanceId(instanceId)
                .orElseThrow(() -> new RuntimeException("工作流实例不存在"));

        instance.setSubtitles(subtitles);
        workflowInstanceRepository.save(instance);
    }

    /**
     * 获取用户在指定时间范围内且具有指定状态的工作流实例
     *
     * @param uid       用户ID
     * @param startTime 开始时间戳
     * @param endTime   结束时间戳
     * @param status    工作流实例状态
     * @return 符合条件的工作流实例列表
     */
    public List<WorkflowInstance> getWorkflowInstancesByUidAndTimeRangeAndStatus(
            String uid, long startTime, long endTime, WorkflowInstanceStatus status) {
        return workflowInstanceRepository.findByUidAndStatusAndCreateTimeBetweenOrderByCreateTimeDesc(
                uid, status, startTime, endTime);
    }

    public List<WorkflowInstance> getWorkflowInstancesByIds(String uid, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return workflowInstanceRepository.findByUidAndWorkflowInstanceIdIn(uid, ids);
    }

    public WorkflowInstance updateWorkflowInstanceStatusWithError(String instanceId, WorkflowInstanceStatus status, String error) {
        return updateWorkflowInstanceStatusAndExternalId(instanceId, status, null, error);
    }

    public WorkflowInstance updateWorkflowInstanceStatus(String instanceId, WorkflowInstanceStatus status) {
        return updateWorkflowInstanceStatusAndExternalId(instanceId, status, null, null);
    }

    /**
     * 更新工作流实例状态和外部ID
     */
    public WorkflowInstance updateWorkflowInstanceStatusAndExternalId(String instanceId, WorkflowInstanceStatus status,
                                                                      String externalId, String error) {
        WorkflowInstance instance = workflowInstanceRepository.findByWorkflowInstanceId(instanceId)
                .orElseThrow(() -> new RuntimeException("工作流实例不存在"));

        instance.setStatus(status);
        if (externalId != null) {
            instance.setExternalId(externalId);
        }

        if (error != null) {
            instance.setError(error);
        }

        return workflowInstanceRepository.save(instance);
    }

    @Transactional
    public List<String> getFileNames(String uid, final String prefix, int times) {
        List<String> res = new ArrayList<>();
        String processedPrefix = prefix.endsWith(".mp4") ? prefix.substring(0, prefix.length() - 4) : prefix;
        // 首先检查记录是否存在，如果不存在则创建一条初始记录
        Optional<FileCounter> existingCounter = fileCounterRepository.findByUidAndType(uid, "file");
        if (existingCounter.isEmpty()) {
            FileCounter initialCounter = new FileCounter();
            initialCounter.setUid(uid);
            initialCounter.setType("file");
            initialCounter.setCount(0L);
            initialCounter.setCreateTime(System.currentTimeMillis());
            initialCounter.setUpdateTime(System.currentTimeMillis());
            fileCounterRepository.save(initialCounter);
        }

        for (int i = 0; i < times; i++) {
            // 使用事务内的操作替代原来的incrementAndGetCounter方法
            // 1. 使用FOR UPDATE锁定记录
            Optional<FileCounter> counterForUpdate = fileCounterRepository.findByUidAndTypeForUpdate(uid, "file");
            if (counterForUpdate.isPresent()) {
                // 2. 增加计数
                FileCounter counter = counterForUpdate.get();
                counter.setCount(counter.getCount() + 1);
                counter.setUpdateTime(System.currentTimeMillis());
                // 3. 保存更新后的记录
                FileCounter updatedCounter = fileCounterRepository.save(counter);
                res.add(String.format("%s--%d.mp4",
                        StringUtils.isNotEmpty(processedPrefix) ? processedPrefix : "default",
                        updatedCounter.getCount()));
            } else {
                // 如果记录不存在（这不应该发生，因为我们在前面已经检查并创建），创建一个新的
                log.error("FileCounter record not found for uid: {} and type: file, despite previous check", uid);
                throw new RuntimeException("Failed to retrieve file counter record");
            }
        }

        return res;
    }

    /**
     * 增加工作流实例的重试次数
     */
    public WorkflowInstance incrementRetryCount(String instanceId) {
        WorkflowInstance instance = workflowInstanceRepository.findByWorkflowInstanceId(instanceId)
                .orElseThrow(() -> new RuntimeException("工作流实例不存在"));

        instance.setRetryTimes(instance.getRetryTimes() + 1);

        return workflowInstanceRepository.save(instance);
    }

    public WorkflowInstance findNewestByUidAndType(String uid, FolderTypeEnum type) {
        return workflowInstanceRepository.findNewestByUidAndType(uid, type, PageRequest.of(0, 1)).stream().findFirst().orElse(null);
    }

    public static String getWorkflowOutputPath(final WorkflowInstance workflowInstance) {
        String folderName = "default";
        if (StringUtils.isNotEmpty(workflowInstance.getOutputDir())) {
            folderName = workflowInstance.getOutputDir();
        }
        return String.format("users/%s/workflow_instances/%s/", workflowInstance.getUid(), folderName);
    }

    /**
     * 分页获取用户工作流实例的输出目录及视频总数
     *
     * @param uid  用户ID
     * @param type 工作流类型
     * @param page 页码，从0开始
     * @param size 每页大小
     * @return 输出目录列表及统计信息
     */
    public List<OutputDirDto> getOutputDirsByPage(String uid, @Nonnull FolderTypeEnum type, int page, int size) {
        List<Object[]> results;
        results = workflowInstanceRepository.findDistinctOutputDirsByUidAndTypeWithVideoCount(uid, type,
                PageRequest.of(page, size));
        return results.stream().map(result -> {
            OutputDirDto dto = new OutputDirDto();
            dto.setOutputDir((String) result[0]);
            dto.setTotalVideoCount(((Number) result[1]).intValue());
            dto.setCreateTime(((Number) result[2]).longValue());
            return dto;
        }).collect(java.util.stream.Collectors.toList());
    }

    @Data
    public static class OutputDirDto {
        private String outputDir;
        private int totalVideoCount;
        private long createTime;
    }

    @Data
    public static class DramaWorkflowParams {
        private List<String> objectKeys;
    }
}