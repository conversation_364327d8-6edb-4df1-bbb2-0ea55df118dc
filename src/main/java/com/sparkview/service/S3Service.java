package com.sparkview.service;

import com.google.common.collect.Maps;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
public class S3Service {

    private final S3Client s3Client;
    private final S3Presigner s3Presigner;

    @Getter
    private final String bucketName;

    @Getter
    private final String region;

    @Getter
    private final String domain;

    public S3Service(
            @Value("${aws.s3.region}") String region,
            @Value("${aws.s3.bucket-name}") String bucketName,
            @Value("${aws.s3.access-key}") String accessKey,
            @Value("${aws.s3.secret-key}") String secretKey,
            @Value("${aws.s3.endpoint}") String endpoint,
            @Value("${aws.s3.domain}") String domain
    ) {

        // Create AWS credentials
        AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(accessKey, secretKey);

        // Create S3 client
        this.s3Client = S3Client.builder()
                .region(Region.of(region))
                .serviceConfiguration(S3Configuration.builder()
                        .accelerateModeEnabled(true)
                        .build())
                .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                .build();
        // Create S3 presigner
        this.s3Presigner = S3Presigner.builder()
                .region(Region.of(region))
                .serviceConfiguration(S3Configuration.builder()
                        .accelerateModeEnabled(true)
                        .build())
                .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                .build();

        this.bucketName = bucketName;
        this.region = region;
        this.domain = domain;
    }


    public ListObjectsV2Response listObjectsWithPagination(String prefix, int maxKeys, String continuationToken) {
        try {
            ListObjectsV2Request.Builder requestBuilder = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(prefix)
                    .maxKeys(maxKeys);

            // 仅在令牌不为空时添加续传令牌
            if (StringUtils.isNotEmpty(continuationToken)) {
                requestBuilder.continuationToken(continuationToken);
            }

            return s3Client.listObjectsV2(requestBuilder.build());
        } catch (S3Exception e) {
            log.error("列举S3对象失败: {}", e.getMessage());
            throw new RuntimeException("获取文件列表失败", e);
        }
    }

    public ListObjectsV2Response listObjectsWithPaginationWithFilter(String prefix, int maxKeys, String continuationToken) {
        try {
            ListObjectsV2Request.Builder requestBuilder = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(prefix)
                    .maxKeys(maxKeys);

            // 仅在令牌不为空时添加续传令牌
            if (StringUtils.isNotEmpty(continuationToken)) {
                requestBuilder.continuationToken(continuationToken);
            }

            ListObjectsV2Response response = s3Client.listObjectsV2(requestBuilder.build());
            List<S3Object> filteredObjects = response.contents().stream()
                    .filter(object -> !object.key().toLowerCase().endsWith(".json"))
                    .collect(Collectors.toList());

            return ListObjectsV2Response.builder()
                    .contents(filteredObjects)
                    .isTruncated(response.isTruncated())
                    .continuationToken(response.continuationToken())
                    .nextContinuationToken(response.nextContinuationToken())
                    .keyCount(filteredObjects.size())  // 更新过滤后的数量
                    .maxKeys(response.maxKeys())
                    .name(response.name())
                    .prefix(response.prefix())
                    .delimiter(response.delimiter())
                    .commonPrefixes(response.commonPrefixes())  // 保留目录前缀
                    .encodingType(response.encodingType())
                    .build();
        } catch (S3Exception e) {
            log.error("列举S3对象失败: {}", e.getMessage());
            throw new RuntimeException("获取文件列表失败", e);
        }
    }

    /**
     * Upload a file to S3
     *
     * @param file   The file to upload
     * @param folder Optional folder path (e.g., "images/")
     * @return The S3 key (path) of the uploaded file
     */
    public String uploadFile(MultipartFile file, String folder) {
        try {
            // Generate a unique file name
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }

            String key = (folder != null ? folder : "") + UUID.randomUUID() + extension;
            Map<String, String> metadata = Maps.newHashMap();
            if (originalFilename != null) {
                metadata.put("original-filename", originalFilename);
            }
            // Upload the file to S3
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(file.getContentType())
                    .metadata(metadata)
                    .build();

            s3Client.putObject(putObjectRequest, RequestBody.fromBytes(file.getBytes()));

            log.info("File uploaded successfully to S3: {}", key);
            return key;
        } catch (IOException e) {
            log.error("Failed to upload file to S3", e);
            throw new RuntimeException("Failed to upload file to S3", e);
        }
    }

    /**
     * Generate a presigned URL for downloading a file
     *
     * @param key               The S3 key (path) of the file
     * @param expirationMinutes How long the URL should be valid (in minutes)
     * @return The presigned URL
     */
    public String generatePresignedDownloadUrl(String key, int expirationMinutes) {
        // 构建 ResponseHeaderOverrides 添加 Content-Disposition 头

        String filename = getFilenameFromKey(key);

        // 构建 GetObject 请求，加入响应头覆盖
        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(bucketName)
                .responseContentDisposition("attachment; filename=\"" + filename + "\"")
                .responseContentType("application/octet-stream")
                .key(key)
                .build();

        GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(Duration.ofMinutes(expirationMinutes))
                .getObjectRequest(getObjectRequest)
                .build();

        PresignedGetObjectRequest presignedRequest = s3Presigner.presignGetObject(presignRequest);
        return presignedRequest.url().toString();
    }

    /**
     * Get the MD5 checksum of an object in S3
     *
     * @param key The S3 key (path) of the object
     * @return The MD5 checksum of the object
     */
    public String getMD5Checksum(String key) {
        GetObjectAttributesRequest request = GetObjectAttributesRequest.builder()
                .bucket(bucketName)
                .key(key)
                .objectAttributes(ObjectAttributes.CHECKSUM)
                .build();

        GetObjectAttributesResponse response = s3Client.getObjectAttributes(request);
        return response.checksum().toString();
    }

    // 在S3Service类中添加此方法
    public int countObjects(String prefix) {
        ListObjectsV2Response response = s3Client.listObjectsV2(builder -> builder
                .bucket(bucketName)
                .prefix(prefix)
                .build());

        // 过滤掉目录本身，只计算实际文件
        return (int) response.contents().stream()
                .filter(obj -> !obj.key().equals(prefix) && !obj.key().endsWith("/"))
                .filter(obj -> !obj.key().toLowerCase().endsWith(".json"))// 排除JSON文件
                .count();
    }


    /**
     * Check if an object exists in S3
     *
     * @param key The S3 key (path) of the object to check
     * @return true if the object exists, false otherwise
     */
    public boolean doesObjectExist(String key) {
        try {
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            s3Client.headObject(headObjectRequest);
            return true;
        } catch (NoSuchKeyException e) {
            // Object does not exist
            return false;
        } catch (S3Exception e) {
            log.error("Error checking if object exists in S3: {}", e.getMessage());
            throw new RuntimeException("Failed to check if object exists in S3", e);
        }
    }

    /**
     * Generate a presigned URL for uploading a file
     *
     * @param key               The S3 key (path) where the file should be uploaded
     * @param expirationMinutes How long the URL should be valid (in minutes)
     * @return The presigned URL
     */
    public String generatePresignedUploadUrl(String key, int expirationMinutes) {
        PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(key)
                .build();

        PutObjectPresignRequest presignRequest = PutObjectPresignRequest.builder()
                .signatureDuration(Duration.ofMinutes(expirationMinutes))
                .putObjectRequest(putObjectRequest)
                .build();

        PresignedPutObjectRequest presignedRequest = s3Presigner.presignPutObject(presignRequest);
        String presignedUrl = presignedRequest.url().toString();

        log.info("Generated presigned upload URL for {}, valid for {} minutes", key, expirationMinutes);
        return presignedUrl;
    }

    public ListObjectsV2Response listObjects(String prefix, int maxKeys, long startAfter) {
        try {
            ListObjectsV2Request request = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(prefix)
                    .maxKeys(maxKeys)
                    .startAfter(String.valueOf(startAfter))
                    .build();

            return s3Client.listObjectsV2(request);
        } catch (S3Exception e) {
            log.error("列举S3对象失败: {}", e.getMessage());
            throw new RuntimeException("获取文件列表失败", e);
        }
    }

    /**
     * 在S3中创建一个目录
     *
     * @param directoryPath 目录路径，应以斜杠结尾
     */
    public void createDirectory(String directoryPath) {
        try {
            // 确保路径以斜杠结尾
            if (!directoryPath.endsWith("/")) {
                directoryPath = directoryPath + "/";
            }

            // 创建一个空的对象来表示目录
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(directoryPath)
                    .build();

            s3Client.putObject(putObjectRequest, RequestBody.empty());

            log.info("Directory created in S3: {}", directoryPath);
        } catch (S3Exception e) {
            log.error("Failed to create directory in S3: {}", e.getMessage());
            throw new RuntimeException("Failed to create directory in S3", e);
        }
    }

    public String uploadBytes(byte[] data, String key, String contentType) {
        try {
            // Upload the byte array to S3
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(contentType)
                    .build();

            s3Client.putObject(putObjectRequest, RequestBody.fromBytes(data));

            log.info("Byte array uploaded successfully to S3: {}", key);
            return key;
        } catch (S3Exception e) {
            log.error("Failed to upload byte array to S3: {}", e.getMessage());
            throw new RuntimeException("Failed to upload byte array to S3", e);
        }
    }


    public S3Client getS3Client() {
        return this.s3Client;
    }

    /**
     * 通过objectKey获取S3对象
     *
     * @param objectKey S3对象的键
     * @return S3Object对象
     */
    public FileInfoDto getFileInfo(String objectKey) {
        try {
            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(objectKey)
                    .build();

            HeadObjectResponse response = s3Client.headObject(headRequest);

            FileInfoDto fileInfo = new FileInfoDto();
            fileInfo.setObjectKey(objectKey);
            fileInfo.setContentLength(response.contentLength());
            fileInfo.setLastModified(response.lastModified());
            fileInfo.setContentType(response.contentType());
            fileInfo.setETag(response.eTag());
            fileInfo.setMetadata(response.metadata());

            return fileInfo;
        } catch (Exception e) {
            log.error("获取对象元数据失败: {}", objectKey, e);
            return null;
        }
    }

    public String updateObjectMetadata(String key, Map<String, String> metadataToUpdate) {
        try {
            // First, get the existing object's metadata
            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            HeadObjectResponse headResponse = s3Client.headObject(headRequest);

            // Create a new metadata map with all existing metadata
            Map<String, String> newMetadata = new HashMap<>(headResponse.metadata());

            // Add or update with the new metadata
            newMetadata.putAll(metadataToUpdate);

            // Use the copy operation to update metadata
            CopyObjectRequest.Builder requestBuilder = CopyObjectRequest.builder()
                    .sourceBucket(bucketName)
                    .sourceKey(key)
                    .destinationBucket(bucketName)
                    .destinationKey(key)
                    .metadata(newMetadata)
                    .metadataDirective(MetadataDirective.REPLACE);

            s3Client.copyObject(requestBuilder.build());

            log.info("Updated metadata for S3 object: {}", key);
            return key;
        } catch (S3Exception e) {
            log.error("Failed to update metadata for S3 object: {}", e.getMessage());
            throw new RuntimeException("Failed to update metadata for S3 object", e);
        }
    }

    /**
     * 获取S3对象的元数据
     *
     * @param key S3对象的键
     * @return 元数据的键值对映射
     */
    public Map<String, String> getObjectMetadata(String key) {
        try {
            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            HeadObjectResponse headResponse = s3Client.headObject(headRequest);

            return headResponse.metadata();
        } catch (S3Exception e) {
            log.error("获取对象元数据失败: {}", e.getMessage());
            return new HashMap<>(); // 返回空映射而不是null，避免NPE
        }
    }


    @Data
    public static class FileInfoDto {
        private String objectKey;
        private Long contentLength;
        private Instant lastModified;
        private String contentType;
        private String eTag;
        private Map<String, String> metadata;

    }

    // 从对象键中提取文件名
    private String getFilenameFromKey(String key) {
        if (key == null || key.isEmpty()) {
            return "download";
        }

        String filename = key;
        int lastSlashIndex = key.lastIndexOf('/');
        if (lastSlashIndex >= 0 && lastSlashIndex < key.length() - 1) {
            filename = key.substring(lastSlashIndex + 1);
        }

        try {
            return URLEncoder.encode(filename, StandardCharsets.UTF_8)
                    .replace("+", "%20");
        } catch (Exception e) {
            return "download";
        }
    }

    /**
     * 获取S3对象的内容并返回为字符串
     *
     * @param key 对象的S3键
     * @return 对象内容的字符串表示
     */
    public String getObjectContent(String key) {
        try {
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            ResponseInputStream<GetObjectResponse> s3Object = s3Client.getObject(getObjectRequest);
            return new String(s3Object.readAllBytes(), StandardCharsets.UTF_8);
        } catch (NoSuchKeyException e) {
            log.error("S3对象不存在: {}", key);
            return null;
        } catch (Exception e) {
            log.error("获取S3对象内容失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取S3对象内容失败", e);
        }
    }

    public List<String> getS3ObjectKeys(String folderPath) {
        ListObjectsV2Response response = listObjects(folderPath, 1000, 0);
        List<S3Object> objects = response.contents();
        return response.contents().stream()
                .filter(obj -> !obj.key().endsWith("/") && !obj.key().isEmpty())
                .map(S3Object::key)
                .collect(Collectors.toList());
    }
}