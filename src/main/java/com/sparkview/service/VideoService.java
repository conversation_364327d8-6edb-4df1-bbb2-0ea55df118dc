package com.sparkview.service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

import com.google.common.collect.Lists;
import com.sparkview.common.FileUtils;
import com.sparkview.common.WorkflowHelper;
import com.sparkview.common.enums.FolderTypeEnum;
import com.sparkview.common.enums.VideoStatusEnum;
import com.sparkview.controller.dto.VideoInfoDto;
import com.sparkview.exception.BusinessException;
import com.sparkview.persistence.Folder;
import com.sparkview.persistence.Video;
import com.sparkview.repository.FolderRepository;
import com.sparkview.repository.VideoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import jakarta.xml.bind.DatatypeConverter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;

@Slf4j
@Service
public class VideoService {
    @Autowired
    private CvService cvService;

    @Autowired
    private TaskExecutor taskExecutor; // 注入线程池

    @Autowired
    private S3Service s3Service;

    @Resource
    private VideoRepository videoRepository;

    @Resource
    private FolderRepository folderRepository;

    private static final int MAX_RETRIES = 3;
    private static final long RETRY_DELAY_MS = 200;

    /**
     * 获取视频时长，支持重试机制
     *
     * @param presignedUrl 预签名URL
     * @return 视频时长
     */
    public double getVideoDurationWithRetry(String presignedUrl) {
        int retryCount = 0;
        double duration = 0.0;

        while (retryCount < MAX_RETRIES) {
            try {
                duration = cvService.getDuration(presignedUrl);
                if (duration > 0) {
                    log.info("Successfully got video duration: {} seconds", duration);
                    return duration;
                }

                retryCount++;
                if (retryCount < MAX_RETRIES) {
                    log.warn("Got zero duration, retrying ({}/{}) after {}ms delay",
                            retryCount, MAX_RETRIES, RETRY_DELAY_MS);
                    Thread.sleep(RETRY_DELAY_MS);
                }
            } catch (Exception e) {
                retryCount++;
                if (retryCount < MAX_RETRIES) {
                    log.warn("Error getting video duration, retrying ({}/{}) after {}ms delay: {}",
                            retryCount, MAX_RETRIES, RETRY_DELAY_MS, e.getMessage());
                    try {
                        Thread.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    log.error("Failed to get video duration after {} retries", MAX_RETRIES, e);
                }
            }
        }

        if (duration == 0) {
            log.error("Failed to get valid video duration after {} retries", MAX_RETRIES);
        }
        return duration;
    }

    public VideoInfoDto getVideoInfoDtoWithPresigned(final String objectKey) {
        VideoInfoDto videoInfoDto = getVideoInfoDto(objectKey);
        // 生成预签名URL
        String presignedUrl = s3Service.generatePresignedDownloadUrl(objectKey, 60); // 1小时有效期
        videoInfoDto.setPresignedUrl(presignedUrl);

        // 短剧添加解析状态
        Optional<Video> optionalVideo = videoRepository.findByObjectKey(objectKey);
        optionalVideo.ifPresent(video -> videoInfoDto.setVideoStatus(video.getStatus()));
        return videoInfoDto;
    }

    public VideoInfoDto getVideoInfoDtoWithoutPresigned(Video video) {
        VideoInfoDto videoInfoDto = new VideoInfoDto();
        String objectKey = video.getObjectKey();
        videoInfoDto.setFileName(FileUtils.extractFileNameFromObjectKey(objectKey));
        videoInfoDto.setObjectKey(objectKey);
        videoInfoDto.setSize(video.getSize());
        videoInfoDto.setLastModified(video.getUpdatedAt());
        videoInfoDto.setCoverKey(video.getCoverKey());
        videoInfoDto.setCoverUrl(video.getCoverUrl());
        videoInfoDto.setAspectRatio(video.getAspectRatio());
        videoInfoDto.setDuration(String.valueOf(video.getDuration()));
        videoInfoDto.setVideoStatus(video.getStatus());
        return videoInfoDto;
    }

    public VideoInfoDto getVideoInfoDto(final String objectKey) {
        VideoInfoDto videoInfoDto = new VideoInfoDto();
        videoInfoDto.setFileName(FileUtils.extractFileNameFromObjectKey(objectKey));
        videoInfoDto.setObjectKey(objectKey);
        // 使用getFileInfo获取对象的元数据
        S3Service.FileInfoDto fileInfo = s3Service.getFileInfo(objectKey);
        if (fileInfo != null && fileInfo.getMetadata() != null) {
            Map<String, String> metadata = fileInfo.getMetadata();
            videoInfoDto.setSize(fileInfo.getContentLength());
            videoInfoDto.setLastModified(fileInfo.getLastModified().toEpochMilli());
            // 设置封面图片的对象键
            String coverKey = metadata.get("coverkey");
            videoInfoDto.setCoverKey(coverKey);

            // 如果有封面图片，生成其预签名URL
            if (coverKey != null && !coverKey.isEmpty()) {
                String coverUrl = s3Service.getDomain() + "/" + coverKey; // 1小时有效期
                videoInfoDto.setCoverUrl(coverUrl);
            }

            // 设置宽高比和时长
            videoInfoDto.setAspectRatio(metadata.get("aspectratio"));
            videoInfoDto.setDuration(metadata.get("duration"));
        }
        return videoInfoDto;
    }

    public CompletableFuture<S3Service.FileInfoDto> makeVideoInfo(String objectKey) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("开始异步处理视频封面生成: {}", objectKey);

                // 1. 生成预签名URL，有效期10分钟
                String presignedUrl = s3Service.generatePresignedDownloadUrl(objectKey, 10);

                // 2. 使用CvService从视频URL中提取第一帧作为封面
                byte[] coverImageBytes = cvService.extractFirstFrame(presignedUrl);

                // 3. 获取视频的宽高比和时长
                String aspectRatio = cvService.getAspectRatio(presignedUrl);
                double duration = getVideoDurationWithRetry(presignedUrl);

                // 4. 计算objectKey的MD5哈希值作为封面图片的key
                String md5Hash = generateMD5(objectKey);
                String coverKey = "static/video_covers/" + md5Hash + ".jpg";

                // 5. 使用新方法直接上传byte数组
                s3Service.uploadBytes(coverImageBytes, coverKey, "image/jpeg");

                // 6. 更新原始视频对象的元数据，添加coverKey、宽高比和时长
                Map<String, String> metadata = new HashMap<>();
                metadata.put("coverkey", coverKey);
                metadata.put("aspectratio", aspectRatio);
                metadata.put("duration", String.format("%.2f", duration)); // 保留两位小数
                s3Service.updateObjectMetadata(objectKey, metadata);

                S3Service.FileInfoDto fileInfoDto = s3Service.getFileInfo(objectKey);
                if (Objects.nonNull(fileInfoDto)) {
                    fileInfoDto.setMetadata(metadata);
                }

                Optional<Video> optionalVideo = videoRepository.findByObjectKey(objectKey);
                if (optionalVideo.isEmpty()) {
                    log.info("no video found in postgresql for objectKey: {}, duration: {}", objectKey, duration);
                } else {
                    Video video = optionalVideo.get();
                    video.setDuration(duration);
                    video.setCoverKey(coverKey);
                    video.setCoverUrl(s3Service.getDomain() + "/" + coverKey);
                    video.setAspectRatio(aspectRatio);
                    video.setSize(fileInfoDto.getContentLength());
                    videoRepository.save(video);
                }

                log.info("视频封面生成和元数据更新完成: {}", objectKey);
                return fileInfoDto;
            } catch (Exception e) {
                log.error("视频封面生成失败: {}", e.getMessage(), e);
                return null;
            }
        }, taskExecutor).orTimeout(120, TimeUnit.SECONDS);
    }

    /**
     * 生成字符串的MD5哈希值
     */
    private String generateMD5(String input) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(input.getBytes());
        byte[] digest = md.digest();
        return DatatypeConverter.printHexBinary(digest).toLowerCase();
    }

    @Transactional
    public void saveVideos(String uid, List<String> videos, FolderTypeEnum folderType, String folderName) {
        Folder folder = folderRepository.findByUidAndTypeAndName(uid, folderType, folderName);

        if (folder == null) {
            log.error("saveVideos error: folder is null, uid: {}, folderType: {}, folderName: {}", uid, folderType, folderName);
            throw new BusinessException(400, "folder is null");
        }

        List<Video> videoList = Lists.newArrayList();
        for (String objectKey : videos) {
            Optional<Video> videoOptional = videoRepository.findByObjectKeyAndUid(objectKey, uid);
            Video video = null;

            if (videoOptional.isPresent()) {
                continue;
            }

            // 不存在存储到数据库
            String name = WorkflowHelper.getFileNameFromPath(objectKey);
            video = Video.builder()
                    .uid(uid)
                    .folderId(String.valueOf(folder.getId()))
                    .folderName(folder.getName())
                    .name(name)
                    .objectKey(objectKey)
                    .status(VideoStatusEnum.INIT.getCode())
                    .build();
            videoList.add(video);
        }
        videoRepository.saveAll(videoList);
    }


    public List<Video> findByFolderId(String folderId) {
        //TODO 其他逻辑待添加
        return videoRepository.findByFolderId(folderId);
    }

    public List<String> findCoverUrlsByFolderId(String folderId) {
        return videoRepository.findCoverUrlsByFolderId(folderId);
    }

    public Map<String, List<String>> findCoverUrlsByFolderIds(List<String> folderIds) {
        List<Object[]> results = videoRepository.findCoverUrlsByFolderIds(folderIds);
        Map<String, List<String>> folderCoversMap = new HashMap<>();
        
        // 初始化所有文件夹的封面列表为空
        for (String folderId : folderIds) {
            folderCoversMap.put(folderId, new ArrayList<>());
        }
        
        // 处理查询结果
        for (Object[] result : results) {
            String folderId = (String) result[0];
            String coverUrl = (String) result[1];
            
            List<String> covers = folderCoversMap.get(folderId);
            if (covers.size() < 4) { // 每个文件夹最多4个封面
                covers.add(coverUrl);
            }
        }
        
        return folderCoversMap;
    }

    /**
     * Migrates metadata from S3 to Video table for historical data
     * This method will find all videos that have missing fields in the database
     * but have metadata in S3, and update the database records accordingly.
     *
     * @return A map containing statistics about the migration process
     */
    @Transactional
    public Map<String, Object> migrateS3MetadataToVideoTable() {
        // 使用默认参数：自动分页处理所有数据
        return migrateS3MetadataToVideoTable(null, 100, false);
    }

    // 存储迁移进度的静态变量
    private static final Map<String, Object> migrationProgress = new HashMap<>();

    /**
     * 获取当前迁移进度
     *
     * @return 包含迁移进度的Map
     */
    public Map<String, Object> getMigrationProgress() {
        return new HashMap<>(migrationProgress);
    }

    /**
     * Migrates metadata from S3 to Video table for historical data with options
     *
     * @param folderIds Optional list of folder IDs to limit the scope of migration
     * @param batchSize Optional batch size for processing (default: process all at once)
     * @param dryRun    If true, only simulate the migration without making changes
     * @return A map containing statistics about the migration process
     */
    @Transactional
    public Map<String, Object> migrateS3MetadataToVideoTable(List<String> folderIds, Integer batchSize, boolean dryRun) {
        // 初始化进度信息
        migrationProgress.clear();
        migrationProgress.put("status", "running");
        migrationProgress.put("startTime", System.currentTimeMillis());
        migrationProgress.put("totalProcessed", 0);
        migrationProgress.put("updatedCount", 0);
        migrationProgress.put("errorCount", 0);
        migrationProgress.put("lastUpdated", System.currentTimeMillis());

        Map<String, Object> result = new HashMap<>();
        int totalProcessed = 0;
        int updatedCount = 0;
        int errorCount = 0;
        List<String> errors = new ArrayList<>();

        try {
            // Get videos from the database based on folder IDs if provided
            List<Video> allVideos;
            if (folderIds != null && !folderIds.isEmpty()) {
                allVideos = new ArrayList<>();
                for (String folderId : folderIds) {
                    allVideos.addAll(videoRepository.findByFolderId(folderId));
                }
                log.info("Found {} videos in specified folders for metadata migration", allVideos.size());
            } else {
                allVideos = videoRepository.findAll();
                log.info("Found {} videos in database for metadata migration", allVideos.size());
            }

            // 更新总数量
            migrationProgress.put("totalVideos", allVideos.size());

            // 确保批处理大小有效
            int batchSizeToUse = batchSize != null && batchSize > 0 ? batchSize : 100; // 默认批量大小为100
            int totalBatches = (int) Math.ceil((double) allVideos.size() / batchSizeToUse);

            // 用于批量保存的列表
            List<Video> videosToUpdate = new ArrayList<>();
            int updateBatchSize = 50; // 每50条记录批量更新一次数据库

            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                int startIndex = batchIndex * batchSizeToUse;
                int endIndex = Math.min(startIndex + batchSizeToUse, allVideos.size());
                List<Video> batchVideos = allVideos.subList(startIndex, endIndex);

                log.info("Processing batch {}/{} with {} videos", batchIndex + 1, totalBatches, batchVideos.size());
                migrationProgress.put("currentBatch", batchIndex + 1);
                migrationProgress.put("totalBatches", totalBatches);

                for (Video video : batchVideos) {
                    totalProcessed++;
                    try {
                        if (video.getDuration() == 0 ||
                                StringUtils.isEmpty(video.getCoverKey()) ||
                                StringUtils.isEmpty(video.getAspectRatio())) {

                            String objectKey = video.getObjectKey();
                            if (StringUtils.isEmpty(objectKey)) {
                                continue; // Skip if no object key
                            }

                            // Get metadata from S3
                            S3Service.FileInfoDto fileInfo = s3Service.getFileInfo(objectKey);
                            if (fileInfo != null && fileInfo.getMetadata() != null) {
                                Map<String, String> metadata = fileInfo.getMetadata();
                                boolean updated = false;

                                // Update duration if needed
                                if (metadata.containsKey("duration")) {
                                    try {
                                        String durationStr = metadata.get("duration");
                                        double duration = Double.parseDouble(durationStr);
                                        video.setDuration(duration);
                                        updated = true;
                                    } catch (NumberFormatException e) {
                                        log.warn("Invalid duration format for video {}: {}", video.getId(), e.getMessage());
                                    }
                                }

                                // Update cover key if needed
                                if (metadata.containsKey("coverkey")) {
                                    String coverKey = metadata.get("coverkey");
                                    video.setCoverKey(coverKey);
                                    video.setCoverUrl(s3Service.getDomain() + "/" + coverKey);
                                    updated = true;
                                }

                                // Update aspect ratio if needed
                                if (metadata.containsKey("aspectratio")) {
                                    video.setAspectRatio(metadata.get("aspectratio"));
                                    updated = true;
                                }

                                // Update file size if needed
                                if (fileInfo.getContentLength() != null) {
                                    video.setSize(fileInfo.getContentLength());
                                    updated = true;
                                } else {
                                    video.setSize(0L);
                                }

                                // 添加到批量更新列表
                                if (updated) {
                                    videosToUpdate.add(video);
                                    updatedCount++;
                                    log.info("Updated metadata for video ID: {}, objectKey: {}", video.getId(), objectKey);

                                    // 当达到批量大小时，执行批量更新
                                    if (videosToUpdate.size() >= updateBatchSize) {
                                        videoRepository.saveAll(videosToUpdate);
                                        videosToUpdate.clear();
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        errorCount++;
                        String errorMsg = "Error processing video ID: " + video.getId() + " - " + e.getMessage();
                        errors.add(errorMsg);
                        log.error(errorMsg, e);
                    }

                    // 每处理10条记录更新一次进度
                    if (totalProcessed % 10 == 0) {
                        updateMigrationProgress(totalProcessed, updatedCount, errorCount);
                    }

                    // Log progress for every 100 videos
                    if (totalProcessed % 100 == 0) {
                        log.info("Processed {} videos, updated {}, errors {}", totalProcessed, updatedCount, errorCount);
                    }
                }

                // 每批次结束后更新进度
                updateMigrationProgress(totalProcessed, updatedCount, errorCount);

                // Add a small delay between batches to avoid overwhelming the system
                if (batchIndex < totalBatches - 1) {
                    try {
                        Thread.sleep(1000); // 1 second delay between batches
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            }

            // 处理剩余的待更新视频
            if (!videosToUpdate.isEmpty()) {
                log.info("Saving remaining {} videos in final batch", videosToUpdate.size());
                videoRepository.saveAll(videosToUpdate);
            }

        } catch (Exception e) {
            log.error("Error during metadata migration", e);
            errors.add("General error: " + e.getMessage());
            errorCount++;
        }

        // 完成迁移，更新最终状态
        migrationProgress.put("status", "completed");
        migrationProgress.put("endTime", System.currentTimeMillis());
        migrationProgress.put("totalProcessed", totalProcessed);
        migrationProgress.put("updatedCount", updatedCount);
        migrationProgress.put("errorCount", errorCount);
        migrationProgress.put("lastUpdated", System.currentTimeMillis());

        result.put("totalProcessed", totalProcessed);
        result.put("updatedCount", updatedCount);
        result.put("errorCount", errorCount);
        result.put("errors", errors);

        long durationMs = (long) migrationProgress.get("endTime") - (long) migrationProgress.get("startTime");
        result.put("durationSeconds", durationMs / 1000);

        log.info("Metadata migration completed. Total: {}, Updated: {}, Errors: {}, Duration: {} seconds",
                totalProcessed, updatedCount, errorCount, durationMs / 1000);

        return result;
    }

    /**
     * 更新迁移进度信息
     */
    private void updateMigrationProgress(int totalProcessed, int updatedCount, int errorCount) {
        migrationProgress.put("totalProcessed", totalProcessed);
        migrationProgress.put("updatedCount", updatedCount);
        migrationProgress.put("errorCount", errorCount);
        migrationProgress.put("lastUpdated", System.currentTimeMillis());

        // 计算进度百分比
        int totalVideos = (int) migrationProgress.get("totalVideos");
        double progressPercent = totalVideos > 0 ? (double) totalProcessed / totalVideos * 100 : 0;
        migrationProgress.put("progressPercent", Math.min(100, Math.round(progressPercent * 100) / 100.0));

        // 计算剩余时间估计
        long startTime = (long) migrationProgress.get("startTime");
        long now = System.currentTimeMillis();
        long elapsed = now - startTime;

        if (totalProcessed > 0 && progressPercent > 0) {
            long estimatedTotalTime = (long) (elapsed / progressPercent * 100);
            long estimatedRemaining = estimatedTotalTime - elapsed;
            migrationProgress.put("estimatedRemainingSeconds", estimatedRemaining / 1000);
        }
    }
}
