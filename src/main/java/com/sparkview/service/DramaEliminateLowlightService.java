package com.sparkview.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sparkview.common.ConfigTemplate;
import com.sparkview.common.JsonUtils;
import com.sparkview.common.enums.LangPatternEnum;
import com.sparkview.controller.dto.HighlightCandidate;
import com.sparkview.controller.dto.TimeFragment;
import com.sparkview.controller.dto.VideoDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DramaEliminateLowlightService {

    @Resource
    private S3Service s3Service;
    @Resource
    private DramaStrategyService dramaStrategyService;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public List<List<VideoDto>> getClipAssetsByDefaultStrategy(String uid, String workflowInstanceId, ConfigTemplate config, List<VideoDto> videos) {
        // 1. 获取剪片策略结果
        List<List<VideoDto>> lists = dramaStrategyService.getClipAssetsByDefaultStrategy(uid, workflowInstanceId, config, videos);
        // 2. 剔除低光处理
        eliminateLowlight(uid, workflowInstanceId, lists);
        return lists;
    }

    /**
     * 剔除低光处理
     */
    private void eliminateLowlight(String uid, String workflowInstanceId, List<List<VideoDto>> lists) {
        if (CollectionUtils.isEmpty(lists)) {
            log.error("eliminateLowlight lists is empty! uid: {}, workflowInstanceId: {}", uid, workflowInstanceId);
            throw new RuntimeException("eliminateLowlight lists is empty!");
        }

        // 依次处理每个成片
        for (List<VideoDto> videoList : lists) {
            // 处理当前视频信息的首尾有效时间
            processVideoStartTimeEndTime(videoList, workflowInstanceId);
            // 统一当前时间片列表
            List<TimeSeries> usefulTimeSeries = getUsefulTimeSeries(videoList);
            // 反向找到符合条件的低光时间片列表
            List<TimeSeries> lowSeries = findLowlightTimeSeries(usefulTimeSeries, videoList, workflowInstanceId);
            // 计算当前视频的最终剪片时长
            BigDecimal totalDuration = calculateTotalDuration(videoList, workflowInstanceId);
            // 根据策略随机剔除时间片
            List<TimeSeries> randomSeries = selectLowSeriesByStrategy(workflowInstanceId, lowSeries, totalDuration, videoList.get(0).getStartTime());
            // 在产生的随机时间片列表中, 进行随机时间段的剪辑获取
            randomSeries = processPerRandomTime(randomSeries, workflowInstanceId);
            // 处理低光时间片, set到最终VideoDto中
            setRandomLowlightTimeSeriesToResult(videoList, randomSeries, workflowInstanceId);
            // 移除中间处理依赖的高光数据信息, 打印日志和请求都不用这个字段
            removeHighlights(videoList);
        }

        log.info("eliminateLowlight workflowInstanceId: {}, lists: {}", workflowInstanceId, lists);
    }

    private void removeHighlights(List<VideoDto> videoList) {
        for (VideoDto videoDto : videoList) {
            videoDto.setHighlightCandidates(null);
        }
    }

    /**
     * 在产生的随机时间片列表中, 进行随机时间段的剪辑获取
     * - 随机剔除时间
     * *     - 最少剔除2s。
     * *     - 最大剔除|低光片段时间-1s|。
     * *     - 剔除时间不包括低光片段两端各0.5s。
     */
    private List<TimeSeries> processPerRandomTime(List<TimeSeries> randomSeries, String workflowInstanceId) {
        if (CollectionUtils.isEmpty(randomSeries)) {
            log.info("processPerRandomTime randomSeries is empty! workflowInstanceId: {}", workflowInstanceId);
            return new ArrayList<>();
        }

        List<TimeSeries> result = new ArrayList<>();
        Random random = new Random();

        for (TimeSeries series : randomSeries) {
            BigDecimal startTime = new BigDecimal(series.getTimeFragment().getStartTime());
            BigDecimal endTime = new BigDecimal(series.getTimeFragment().getEndTime());
            BigDecimal duration = endTime.subtract(startTime);

            // 如果总时长小于3秒（两端各0.5s + 最小剔除2s），则跳过这个片段
            if (duration.compareTo(new BigDecimal("3")) < 0) {
                log.info("processPerRandomTime duration too short, skip. workflowInstanceId: {}, videoId: {}, duration: {}",
                        workflowInstanceId, series.getVideoId(), duration);
                continue;
            }

            // 1. 先开头结尾各自缩小0.5s范围
            BigDecimal newStartTime = startTime.add(new BigDecimal("0.5"));
            BigDecimal newEndTime = endTime.subtract(new BigDecimal("0.5"));
            BigDecimal newDuration = newEndTime.subtract(newStartTime);

            // 2. 随机剔除时间
            // 计算可剔除的最大时长：总时长-1秒
            BigDecimal maxRemoveDuration = newDuration.subtract(new BigDecimal("1"));
            // 确保最大剔除时长不小于最小剔除时长（2秒）
            if (maxRemoveDuration.compareTo(new BigDecimal("2")) < 0) {
                log.info("processPerRandomTime maxRemoveDuration too small, skip. workflowInstanceId: {}, videoId: {}, maxRemoveDuration: {}",
                        workflowInstanceId, series.getVideoId(), maxRemoveDuration);
                continue;
            }

            // 随机生成剔除时长（2秒到maxRemoveDuration之间）
            BigDecimal removeDuration = new BigDecimal("2").add(new BigDecimal(String.valueOf(random.nextDouble())).multiply(maxRemoveDuration.subtract(new BigDecimal("2")))).setScale(3, RoundingMode.HALF_UP);

            // 随机选择剔除的起始时间点
            BigDecimal maxStartTime = newEndTime.subtract(removeDuration);
            BigDecimal randomStartTime = newStartTime.add(new BigDecimal(String.valueOf(random.nextDouble())).multiply(maxStartTime.subtract(newStartTime))).setScale(3, RoundingMode.HALF_UP);

            // 创建新的时间片段
            TimeFragment newTimeFragment = new TimeFragment(
                    randomStartTime.toString(),
                    randomStartTime.add(removeDuration).toString()
            );
            result.add(new TimeSeries(series.getVideoId(), newTimeFragment));
        }

        log.info("processPerRandomTime completed. workflowInstanceId: {}, original size: {}, result size: {}",
                workflowInstanceId, randomSeries.size(), result.size());
        return result;
    }

    /**
     * 将最终的随机低光片段set到最终的VideoDto中, 供剪辑使用
     */
    private void setRandomLowlightTimeSeriesToResult(List<VideoDto> videoList, List<TimeSeries> randomSeries, String workflowInstanceId) {
        if (CollectionUtils.isEmpty(randomSeries)) {
            log.info("setRandomLowlightTimeSeriesToResult randomSeries is empty! workflowInstanceId: {}", workflowInstanceId);
            return;
        }

        // 1. 将randomSeries中的时间片, 按照视频id进行分组
        Map<Long, List<TimeSeries>> videoIdToTimeSeriesMap = randomSeries.stream()
                .collect(Collectors.groupingBy(TimeSeries::getVideoId));

        // 2. 将每个视频id对应的时间片, 按照时间片的startTime进行排序
        videoIdToTimeSeriesMap.forEach((videoId, timeSeriesList) -> {
            // 按startTime排序
            timeSeriesList.sort(Comparator.comparing(series ->
                    new BigDecimal(series.getTimeFragment().getStartTime())));

            // 3. 将每个视频排序后的时间片组合成videoList的字段private List<TimeFragment> lowlightList格式并set进去
            List<TimeFragment> lowlightList = timeSeriesList.stream()
                    .map(TimeSeries::getTimeFragment)
                    .collect(Collectors.toList());

            // 找到对应的VideoDto并设置lowlightList
            videoList.stream()
                    .filter(video -> video.getId().equals(videoId))
                    .findFirst()
                    .ifPresent(video -> video.setLowlightList(lowlightList));
        });

        log.info("setRandomLowlightTimeSeriesToResult completed. workflowInstanceId: {}, videoList size: {}, randomSeries size: {}",
                workflowInstanceId, videoList.size(), randomSeries.size());
    }

    private void processVideoStartTimeEndTime(List<VideoDto> videoList, String workflowInstanceId) {
        for (VideoDto video : videoList) {
            if (StringUtils.isBlank(video.getEndTime())) {
                double duration = video.getDuration();
                if (duration <= 0) {
                    log.warn("processVideoStartTimeEndTime duration is invalid! workflowInstanceId: {}, videoId: {}, duration: {}",
                            workflowInstanceId, video.getId(), duration);
                    throw new RuntimeException("calculateTotalDuration duration is invalid! videoId: " + video.getId());
                }

                video.setEndTime(String.valueOf(duration));
            }

            if (StringUtils.isBlank(video.getStartTime())) {
                video.setStartTime("0");
            }
        }
    }

    /**
     * 根据策略随机剔除时间片
     *
     * @param workflowInstanceId 当前任务实例id，用于日志打印
     * @param lowSeries          当前视频的低光时间片段集合
     * @param totalDuration      当前视频的最终剪片时长
     * @param startTime          当前视频的开始时间
     * @return 筛选出的时间片段
     */
    private List<TimeSeries> selectLowSeriesByStrategy(String workflowInstanceId, List<TimeSeries> lowSeries, BigDecimal totalDuration, String startTime) {
        if (CollectionUtils.isEmpty(lowSeries)) {
            log.info("selectLowSeriesByStrategy lowSeries is empty! workflowInstanceId: {}", workflowInstanceId);
            return new ArrayList<>();
        }

        // 步骤1：过滤掉前30秒的低光片段
        BigDecimal thirtySeconds = new BigDecimal("30");
        BigDecimal videoStartTime = new BigDecimal(startTime);
        BigDecimal thirtySecondsEnd = videoStartTime.add(thirtySeconds);

        List<TimeSeries> filteredLowSeries = lowSeries.stream()
                .filter(series -> {
                    BigDecimal seriesStart = new BigDecimal(series.getTimeFragment().getStartTime());
                    BigDecimal seriesEnd = new BigDecimal(series.getTimeFragment().getEndTime());
                    // 如果片段结束时间小于等于视频开始时间，或者片段开始时间大于等于30秒结束时间，则保留
                    return seriesEnd.compareTo(videoStartTime) <= 0 || seriesStart.compareTo(thirtySecondsEnd) >= 0;
                })
                .collect(Collectors.toList());

        log.info("selectLowSeriesByStrategy after filter lowSeries size: {}, lowSeries: {}, workflowInstanceId: {}", filteredLowSeries.size(), lowSeries.size(), workflowInstanceId);

        if (CollectionUtils.isEmpty(filteredLowSeries)) {
            log.info("selectLowSeriesByStrategy filteredLowSeries is empty! workflowInstanceId: {}", workflowInstanceId);
            return new ArrayList<>();
        }

        // 步骤2：根据视频时长确定上限N
        int toRemoveCount;
        int lowSeriesCount = filteredLowSeries.size();

        // 根据低光片段数量确定要剔除的数量
        if (lowSeriesCount <= 3) {
            toRemoveCount = lowSeriesCount; // 全部剔除
            log.info("selectLowSeriesByStrategy lowSeriesCount <= 3, toRemoveCount: {}, workflowInstanceId: {}", toRemoveCount, workflowInstanceId);
        } else {
            int maxNSubtract1 = getMaxNSubtract1ByDuration(totalDuration, workflowInstanceId);
            toRemoveCount = Math.min(lowSeriesCount - 1, maxNSubtract1); // 随机剔除N-1个，但不超过上限
            log.info("selectLowSeriesByStrategy maxNSubtract1: {}, toRemoveCount: {}, workflowInstanceId: {}", maxNSubtract1, toRemoveCount, workflowInstanceId);
        }

        // 步骤3：随机选择要剔除的片段
        List<TimeSeries> result = new ArrayList<>(filteredLowSeries);
        if (toRemoveCount <= result.size()) {
            // 随机打乱列表
            Collections.shuffle(result);
            // 前toRemoveCount个作为剔除的片段(shuffle后本身就是随机的)
            result = result.subList(0, toRemoveCount);
            log.info("selectLowSeriesByStrategy after shuffle, toRemoveCount: {}, filteredLowSeries size: {}, workflowInstanceId: {}", toRemoveCount, filteredLowSeries.size(), workflowInstanceId);
        } else {
            result.clear(); // 如果全部剔除，返回空列表
        }

        log.info("selectLowSeriesByStrategy completed. workflowInstanceId: {}, original count: {}, filtered count: {}, to remove: {}, final count: {}, result: {}",
                workflowInstanceId, lowSeries.size(), filteredLowSeries.size(), toRemoveCount, result.size(), JsonUtils.toJson(result));

        return result;
    }

    /**
     * 根据视频时长获取最大剔除数量(N-1): 比如现在有6个低光片段，需要随机剔除5个，那么N=6，N-1=5. 目的是随机6个里挑5个
     *
     * @return 返回N-1的限制值
     */
    private int getMaxNSubtract1ByDuration(BigDecimal duration, String workflowInstanceId) {
        // 时长格式为"秒.毫秒"，例如123.2424表示123秒2424毫秒
        // 将秒转换为分钟，保留2位小数
        BigDecimal durationInMinutes = duration.divide(new BigDecimal("60"), 2, RoundingMode.HALF_UP);

        // 根据成片时长，（N-1）必须小于等于上限
        if (durationInMinutes.compareTo(new BigDecimal("2")) < 0) {
            // 小于2分钟的视频不进行低光剔除
            log.info("getMaxNByDuration duration < 2min, durationInMinutes: {}, return: 0, workflowInstanceId: {}",
                    durationInMinutes, workflowInstanceId);
            return 3;
        } else if (durationInMinutes.compareTo(new BigDecimal("3.5")) <= 0) {
            // 2-3.5分钟的视频，最多剔除4个低光片段
            log.info("getMaxNByDuration 2min <= duration <= 3.5min, durationInMinutes: {}, return: 4, workflowInstanceId: {}",
                    durationInMinutes, workflowInstanceId);
            return 4;
        } else if (durationInMinutes.compareTo(new BigDecimal("5")) <= 0) {
            // 3.5-5分钟的视频，最多剔除6个低光片段
            log.info("getMaxNByDuration 3.5min < duration <= 5min, durationInMinutes: {}, return: 6, workflowInstanceId: {}",
                    durationInMinutes, workflowInstanceId);
            return 6;
        } else if (durationInMinutes.compareTo(new BigDecimal("7.5")) <= 0) {
            // 5-7.5分钟的视频，最多剔除9个低光片段
            log.info("getMaxNByDuration 5min < duration <= 7.5min, durationInMinutes: {}, return: 9, workflowInstanceId: {}",
                    durationInMinutes, workflowInstanceId);
            return 9;
        } else if (durationInMinutes.compareTo(new BigDecimal("10")) <= 0) {
            // 7.5-10分钟的视频，最多剔除11个低光片段
            log.info("getMaxNByDuration 7.5min < duration <= 10min, durationInMinutes: {}, return: 11, workflowInstanceId: {}",
                    durationInMinutes, workflowInstanceId);
            return 11;
        } else if (durationInMinutes.compareTo(new BigDecimal("15")) <= 0) {
            // 10-15分钟的视频，最多剔除14个低光片段
            log.info("getMaxNByDuration 10min < duration <= 15min, durationInMinutes: {}, return: 14, workflowInstanceId: {}",
                    durationInMinutes, workflowInstanceId);
            return 14;
        } else if (durationInMinutes.compareTo(new BigDecimal("20")) <= 0) {
            // 15-20分钟的视频，最多剔除17个低光片段
            log.info("getMaxNByDuration 15min < duration <= 20min, durationInMinutes: {}, return: 17, workflowInstanceId: {}",
                    durationInMinutes, workflowInstanceId);
            return 17;
        } else {
            // 20分钟以上的视频，暂时使用20分钟的上限（17个低光片段）
            log.info("getMaxNByDuration duration > 20min, durationInMinutes: {}, return: 17, workflowInstanceId: {}",
                    durationInMinutes, workflowInstanceId);
            return 17;
        }
    }

    /**
     * 计算当前视频的最终剪片时长
     */
    private BigDecimal calculateTotalDuration(List<VideoDto> videoList, String workflowInstanceId) {
        BigDecimal totalDuration = BigDecimal.ZERO;
        for (VideoDto video : videoList) {
            try {
                BigDecimal startTime = new BigDecimal(video.getStartTime());
                BigDecimal endTime = new BigDecimal(video.getEndTime());
                BigDecimal duration = endTime.subtract(startTime);

                if (duration.compareTo(BigDecimal.ZERO) > 0) {
                    totalDuration = totalDuration.add(duration);
                    log.info("calculateTotalDuration videoId: {}, startTime: {}, endTime: {}, duration: {}, totalDuration: {}",
                            video.getId(), video.getStartTime(), video.getEndTime(), duration, totalDuration);
                } else {
                    log.error("calculateTotalDuration duration is not positive! workflowInstanceId: {}, videoId: {}, startTime: {}, endTime: {}",
                            workflowInstanceId, video.getId(), video.getStartTime(), video.getEndTime());
                    throw new RuntimeException("calculateTotalDuration duration is not positive! videoId: " + video.getId());
                }
            } catch (NumberFormatException e) {
                log.error("calculateTotalDuration parse time error! workflowInstanceId: {}, videoId: {}, startTime: {}, endTime: {}",
                        workflowInstanceId, video.getId(), video.getStartTime(), video.getEndTime(), e);
                throw new RuntimeException("calculateTotalDuration parse time error! videoId: " + video.getId());
            }
        }

        // 将BigDecimal转换为long，四舍五入到最接近的整数
        return totalDuration;
    }

    /**
     * 1、先找到符合条件的高光数据 + asr时间点数据
     * 2、将上述时间片段求差集反向得到差集序列
     * 3、在差中过滤掉间隔时间少于5s的低光片段
     */
    private List<TimeSeries> findLowlightTimeSeries(List<TimeSeries> usefulTimeSeries, List<VideoDto> videoList, String workflowInstanceId) {
        // 构建videoList的Map
        Map<Long, VideoDto> videoMap = videoList.stream().collect(Collectors.toMap(VideoDto::getId, video -> video));
        // 先根据TimeSeries的videoId分组
        Map<Long, List<TimeSeries>> timeSeriesMap = usefulTimeSeries.stream().collect(java.util.stream.Collectors.groupingBy(TimeSeries::getVideoId));
        // 结果
        List<TimeSeries> result = new ArrayList<>();

        // 遍历每个分组
        for (Map.Entry<Long, List<TimeSeries>> entry : timeSeriesMap.entrySet()) {
            Long videoId = entry.getKey();
            List<TimeSeries> highlightSeriesList = entry.getValue();
            VideoDto videoDto = videoMap.get(videoId);
            String curVideoEffectiveStartTime = videoDto.getStartTime();
            String curVideoEffectiveEndTime = videoDto.getEndTime();

            // 1. 将高光时间片按开始时间排序
            highlightSeriesList.sort(Comparator.comparingDouble(a -> Double.parseDouble(a.getTimeFragment().getStartTime())));

            // 2. 找出所有低光区间
            List<TimeSeries> lowlightSeries = new ArrayList<>();
            BigDecimal currentTime = new BigDecimal(curVideoEffectiveStartTime);

            for (TimeSeries highlight : highlightSeriesList) {
                BigDecimal highlightStart = new BigDecimal(highlight.getTimeFragment().getStartTime());
                BigDecimal highlightEnd = new BigDecimal(highlight.getTimeFragment().getEndTime());

                // 如果当前时间小于高光开始时间，说明中间有低光区间
                if (currentTime.compareTo(highlightStart) < 0) {
                    lowlightSeries.add(new TimeSeries(videoId,
                            new TimeFragment(currentTime.toString(), highlightStart.toString())));
                }
                // 更新当前时间为高光结束时间
                currentTime = currentTime.max(highlightEnd);
            }

            // 处理最后一个高光片段到视频结束的时间
            BigDecimal videoEndTime = new BigDecimal(curVideoEffectiveEndTime);
            if (currentTime.compareTo(videoEndTime) < 0) {
                lowlightSeries.add(new TimeSeries(videoId,
                        new TimeFragment(currentTime.toString(), curVideoEffectiveEndTime)));
            }

            // 3. 合并重叠的低光区间
            List<TimeSeries> mergedLowlightSeries = new ArrayList<>();
            if (!lowlightSeries.isEmpty()) {
                TimeSeries current = lowlightSeries.get(0);

                for (int i = 1; i < lowlightSeries.size(); i++) {
                    TimeSeries next = lowlightSeries.get(i);
                    BigDecimal currentEnd = new BigDecimal(current.getTimeFragment().getEndTime());
                    BigDecimal nextStart = new BigDecimal(next.getTimeFragment().getStartTime());

                    // 如果当前区间结束时间大于等于下一个区间的开始时间，则合并
                    if (currentEnd.compareTo(nextStart) >= 0) {
                        BigDecimal nextEnd = new BigDecimal(next.getTimeFragment().getEndTime());
                        // 使用较大的结束时间
                        String mergedEndTime = currentEnd.compareTo(nextEnd) >= 0 ?
                                current.getTimeFragment().getEndTime() :
                                next.getTimeFragment().getEndTime();

                        current = new TimeSeries(videoId, new TimeFragment(
                                current.getTimeFragment().getStartTime(),
                                mergedEndTime
                        ));
                    } else {
                        mergedLowlightSeries.add(current);
                        current = next;
                    }
                }
                mergedLowlightSeries.add(current);
            }

            // 4. 过滤掉持续时间小于5秒的低光区间
            BigDecimal minDuration = new BigDecimal("5.0");
            for (TimeSeries series : mergedLowlightSeries) {
                BigDecimal startTime = new BigDecimal(series.getTimeFragment().getStartTime());
                BigDecimal endTime = new BigDecimal(series.getTimeFragment().getEndTime());
                BigDecimal duration = endTime.subtract(startTime);

                if (duration.compareTo(minDuration) >= 0) {
                    result.add(series);
                }
            }

            log.info("findLowlightTimeSeries videoId: {}, lowlightSeries size: {}, mergedLowlightSeries size: {}, result size: {}",
                    videoId, lowlightSeries.size(), mergedLowlightSeries.size(), result.size());
        }

        log.info("findLowlightTimeSeries completed. result size: {}, workflowInstanceId: {}, result: {}", result.size(), workflowInstanceId, JsonUtils.toJson(result));
        return result;
    }

    /**
     * 统一时间片列表.
     * 需要将高光片段和asr内容时间进行统一格式, 这些都是要保留的片段
     */
    private List<TimeSeries> getUsefulTimeSeries(List<VideoDto> videoList) {
        List<TimeSeries> timeSeries = new ArrayList<>();

        for (VideoDto videoDto : videoList) {
            if (videoDto.getDuration() <= 0) {
                log.error("videoDto.getDuration() <= 0! videoId: {}, duration: {}", videoDto.getId(), videoDto.getDuration());
                throw new RuntimeException("videoDto.getDuration() <= 0, videoId: " + videoDto.getId());
            }

            // 1. 先处理高光时间片
            List<HighlightCandidate> highlightList = videoDto.getHighlightCandidates();
            if (CollectionUtils.isNotEmpty(highlightList)) {
                for (HighlightCandidate highlightDto : highlightList) {
                    // 如果高光时间片在有效时间之前 or 高光时间片在有效时间之后, 跳过, 跳过
                    if (new BigDecimal(highlightDto.getEndTime()).compareTo(new BigDecimal(videoDto.getStartTime())) <= 0 ||
                            new BigDecimal(highlightDto.getStartTime()).compareTo(new BigDecimal(videoDto.getEndTime())) >= 0) {
                        continue;
                    }

                    // 保留分值高的片段
                    if (matchHighlightByScore(highlightDto)) {
                        timeSeries.add(new TimeSeries(videoDto.getId(), new TimeFragment(highlightDto.getStartTime(), highlightDto.getEndTime())));
                    }
                }
            }

            // 2. 再处理asr时间片
            List<SpeechToTextWordResponseModel> wordList = getWordList(videoDto.getLang(), videoDto.getId(), videoDto.getObject_key());
            if (CollectionUtils.isNotEmpty(wordList)) {
                for (SpeechToTextWordResponseModel speech : wordList) {
                    // 如果台词时间片在有效时间之前 or 台词时间片在有效时间之后, 跳过, 跳过
                    if (new BigDecimal(String.valueOf(speech.getEnd())).compareTo(new BigDecimal(videoDto.getStartTime())) <= 0 ||
                            new BigDecimal(String.valueOf(speech.getStart())).compareTo(new BigDecimal(videoDto.getEndTime())) >= 0) {
                        continue;
                    }

                    timeSeries.add(new TimeSeries(videoDto.getId(), new TimeFragment(String.valueOf(speech.getStart()), String.valueOf(speech.getEnd()))));
                }
            }

            // 3. 处理开头和结尾各10s。这两个区间也不能作为低光片段
            BigDecimal startTime = new BigDecimal(videoDto.getStartTime());
            BigDecimal endTime = new BigDecimal(videoDto.getEndTime());
            BigDecimal ten = new BigDecimal("10.0");

            // 开始时间在0-10s之间, 保留startTime~10s范围片段
            if (startTime.compareTo(ten) <= 0) {
                timeSeries.add(new TimeSeries(videoDto.getId(), new TimeFragment(videoDto.getStartTime(), "10.0")));
            }

            // 结束时间在倒数10s-duration之间, 保留倒数10s～endTime
            BigDecimal endSubtractTen = new BigDecimal(String.valueOf(videoDto.getDuration())).subtract(ten);
            if (endTime.compareTo(endSubtractTen) >= 0) {
                timeSeries.add(new TimeSeries(videoDto.getId(), new TimeFragment(endSubtractTen.toString(), videoDto.getEndTime())));
            }
        }

        return timeSeries;
    }

    /**
     * 根据source类型选择符合score的高光片段
     *
     * @return true: 符合条件, false: 不符合条件
     */
    private boolean matchHighlightByScore(HighlightCandidate highlightDto) {
        return switch (highlightDto.getSource()) {
            case Textover, Vision ->
                // 视觉和台词都属于不可剔除的片段
                    true;
            case Acoustic ->
                // 场景片段, 保留score大于等于0.5的片段
                    compareWithBigDecimal(highlightDto.getPriorityScore(), 7.0d) >= 0;
        };
    }

    /**
     * 比较大小
     */
    public static int compareWithBigDecimal(double a, double b) {
        // 必须使用字符串构造 BigDecimal，避免直接传入 double 导致精度丢失:ml-citation{ref="4,7" data="citationList"}
        BigDecimal num1 = new BigDecimal(Double.toString(a));
        BigDecimal num2 = new BigDecimal(Double.toString(b));
        return num1.compareTo(num2); // 返回 -1/0/1 表示大小关系
    }

    /**
     * 将视频文件路径转换为对应的json文件路径
     * 例如：video.mp4 -> video.json
     * video.mov -> video.json
     * video.avi -> video.json
     */
    private String convertToJsonPath(String videoPath) {
        if (StringUtils.isBlank(videoPath)) {
            return videoPath;
        }
        // 找到最后一个点号的位置
        int lastDotIndex = videoPath.lastIndexOf('.');
        if (lastDotIndex == -1) {
            // 如果没有找到点号，直接添加.json
            return videoPath + ".json";
        }
        // 替换最后一个点号后的所有内容为.json
        return videoPath.substring(0, lastDotIndex) + ".json";
    }

    /**
     * 将json文本转为SpeechToTextWordResponseModel集合
     */
    public List<SpeechToTextWordResponseModel> getWordList(String lang, Long videoId, String objectKey) {
        //将objectKey中的视频后缀换成.json
        String jsonObjectKey = convertToJsonPath(objectKey);
        String jsonStr = getAsrJson(jsonObjectKey, videoId);
        LangPatternEnum langPatternEnum = LangPatternEnum.fromValue(lang);

        if (langPatternEnum == null) {
            log.error("getWordList LangPatternEnum is null! no support for lang: {}, videoId: {}, objectKey: {}", lang, videoId, objectKey);
            throw new IllegalArgumentException("Lang does not exist: " + lang);
        }

        return langPatternEnum == LangPatternEnum.CHINESE ? parseByZh(jsonStr, videoId, objectKey) : parseByEn(jsonStr, videoId, objectKey);
    }

    /**
     * 从 S3 存储中获取指定键对应的 ASR（自动语音识别）JSON 内容。
     *
     * @param key S3 对象的键，用于定位特定的 ASR JSON 文件。
     * @return 若成功获取，返回 ASR JSON 内容的字符串表示；若出现异常，抛出异常。
     */
    public String getAsrJson(String key, Long videoId) {
        try {
            // 调用 S3Service 的 getObjectContent 方法获取指定键的对象内容
            String asrJson = s3Service.getObjectContent(key);

            if (StringUtils.isBlank(asrJson) || "null".equals(asrJson)) {
                log.error("getAsrJson object content is empty, key: {}, videoId: {}", key, videoId);
                throw new RuntimeException(String.format("s3 asr is empty. key: %s, videoId: %s", key, videoId));
            }

            return asrJson;
        } catch (Exception e) {
            // 记录获取 ASR JSON 内容失败的错误信息
            log.error("getAsrJson object error, key: {}, videoId: {}", key, videoId, e);
            throw new RuntimeException(String.format("s3 asr is empty. key: %s, videoId: %s", key, videoId));
        }
    }

    /**
     * 处理asr外文接口数据格式转换
     */
    private List<SpeechToTextWordResponseModel> parseByEn(String jsonStr, Long videoId, String objectKey) {
        if (StringUtils.isBlank(jsonStr)) {
            log.error("Parse English ASR is empty. videoId: {}, objectKey: {}, jsonStr: {}", videoId, objectKey, jsonStr);
            throw new RuntimeException("Parse English ASR JSON empty, " + "videoId:" + videoId + "objectKey:" + objectKey);
        }

        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            JsonNode wordsNode = jsonNode.get("words");

            List<SpeechToTextWordResponseModel> resultList = new ArrayList<>();
            for (JsonNode word : wordsNode) {
                if ("word".equals(word.get("type").asText())) {
                    resultList.add(SpeechToTextWordResponseModel.builder()
                            .text(word.get("text").asText())
                            .start(word.get("start").asDouble())
                            .end(word.get("end").asDouble())
                            .type(word.get("type").asText())
                            .speakerId(word.has("speaker_id") ? word.get("speaker_id").asText() : null)
                            .build());
                }
            }
            return resultList;
        } catch (Exception e) {
            log.error("Parse English ASR JSON error. videoId: {}, objectKey: {}, jsonStr: {}", videoId, objectKey, jsonStr, e);
            throw new RuntimeException("Parse English ASR JSON error, " + "videoId:" + videoId + "objectKey:" + objectKey);
        }
    }

    /**
     * 处理asr中文接口数据格式转换
     */
    private List<SpeechToTextWordResponseModel> parseByZh(String jsonStr, Long videoId, String objectKey) {
        if (StringUtils.isBlank(jsonStr)) {
            log.error("Parse Chinese ASR JSON empty. videoId: {}, objectKey: {}, jsonStr: {}", videoId, objectKey, jsonStr);
            throw new RuntimeException("Parse Chinese ASR JSON empty, " + "videoId:" + videoId + "objectKey:" + objectKey);
        }

        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            JsonNode utterancesNode = jsonNode.get("utterances");

            List<SpeechToTextWordResponseModel> resultList = new ArrayList<>();
            for (JsonNode utterance : utterancesNode) {
                if ("speech".equals(utterance.get("attribute").get("event").asText())) {
                    JsonNode wordsNode = utterance.get("words");
                    for (JsonNode word : wordsNode) {
                        if ("speech".equals(word.get("attribute").get("event").asText())) {
                            resultList.add(SpeechToTextWordResponseModel.builder()
                                    .text(word.get("text").asText())
                                    .start(word.get("start_time").asDouble() / 1000)
                                    .end(word.get("end_time").asDouble() / 1000)
                                    .type("word")
                                    .build());
                        }
                    }
                }
            }
            return resultList;
        } catch (Exception e) {
            log.error("Parse Chinese ASR JSON error. videoId: {}, objectKey: {}, jsonStr: {}", videoId, objectKey, jsonStr, e);
            throw new RuntimeException("Parse Chinese ASR JSON error, " + "videoId:" + videoId + "objectKey:" + objectKey);
        }
    }

    /**
     * 函数内部领域处理语音文本对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SpeechToTextWordResponseModel {
        private String text;
        private double start;
        private double end;
        private String type;
        private String speakerId;
    }

    @Data
    @AllArgsConstructor
    public static class TimeSeries {
        private Long videoId;
        private TimeFragment timeFragment;
    }
}
