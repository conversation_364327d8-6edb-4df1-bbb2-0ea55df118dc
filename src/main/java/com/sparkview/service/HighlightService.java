package com.sparkview.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.sparkview.common.*;
import com.sparkview.common.enums.FolderTypeEnum;
import com.sparkview.common.enums.VideoStatusEnum;
import com.sparkview.controller.FolderController;
import com.sparkview.controller.dto.VideoDto;
import com.sparkview.persistence.*;
import com.sparkview.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @author: gavingeng
 * @create: 2025-04-18 18:02
 * @description:
 **/
@Service
@Slf4j
public class HighlightService {

    private static final int BATCH_SIZE = 2;

    @Value("${perfect.api.highlightUrl:http://localhost:8000/api/workflow/highlight}")
    private String highlightUrl;

    @Value("${perfect.api.acousticUrl:http://localhost:8000/api/workflow/acoustic}")
    private String acousticUrl;

    @Value("${perfect.api.effectUrl:http://localhost:8000/api/workflow/effect}")
    private String effectUrl;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private FolderRepository folderRepository;

    @Resource
    private VideoService videoService;

    @Resource
    private HighlightRepository highlightRepository;

    @Resource
    private VideoRepository videoRepository;

    @Resource
    private WorkflowInstanceRepository workflowInstanceRepository;

    @Resource
    private EffectsRepository effectsRepository;

    @Resource
    private S3Service s3Service;

    @Resource
    private CvService cvService;

    @Transactional
    public void startHighlightWorkflow(String workflowInstanceId) {
        Map<String, Object> workflowParams = getWorkflowParams(workflowInstanceId);
        try {
            CompletableFuture.runAsync(() -> {
                try {
                    restTemplate.postForLocation(highlightUrl, workflowParams, String.class);
                } catch (Exception e) {
                    // 简单记录日志或不处理
                    log.error("异步请求失败: {}", e.getMessage());
                }
            });
            log.info("Start highlight workflow by Prefect API, workflowInstanceId: {}", workflowInstanceId);
        } catch (Exception e) {
            log.error("Error starting highlight workflow by Prefect API: {}", e.getMessage());
            e.printStackTrace();
        }
    }

    public void startHighlightWorkflow(WorkflowInstance workflowInstance, String folderId, int dramaType, String lang) {
        String uid = RequestContext.getCurrentUser();
        List<Video> videoList = videoRepository.findByFolderIdAndStatusAndUid(folderId, VideoStatusEnum.INIT.getCode(), uid);

        List<List<Video>> batches = new ArrayList<>();

        // Step 1: 分批，每BATCH_SIZE个一组
        for (int i = 0; i < videoList.size(); i += BATCH_SIZE) {
            int end = Math.min(i + BATCH_SIZE, videoList.size());
            batches.add(new ArrayList<>(videoList.subList(i, end)));
        }

        // Step 2: 执行请求，每个批次单独执行
        for (int i = 0; i < batches.size(); i++) {
            List<Video> batch = batches.get(i);
            Map<String, Object> workflowParams = getWorkflowParams(workflowInstance, batch, dramaType, lang);
            try {
                restTemplate.postForLocation(highlightUrl, workflowParams, String.class);
                log.info("Started highlight workflow batch {}, size: {}, workflowInstanceId: {}", i + 1, batch.size(), workflowInstance.getWorkflowInstanceId());
            } catch (Exception e) {
                log.error("Error starting highlight workflow batch {}: {}", i + 1, e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 处理特效的source=1的情况
     *
     * @param workflowInstanceId
     */
    public void startEffectWorkflow(String workflowInstanceId) {
        WorkflowInstance workflowInstance = workflowInstanceRepository.findByWorkflowInstanceId(workflowInstanceId)
                .orElseThrow(() -> new RuntimeException("error"));
        Map<String, Object> workflowParams = getEffectWorkflowParams(workflowInstance);
        try {
            List videos = (List) workflowParams.get("videos");
            if (videos.isEmpty()) {
                log.info("startEffectWorkflow:video is null，workflowInstanceId: {}", workflowInstanceId);
                return;
            }

            CompletableFuture.runAsync(() -> {
                try {
                    restTemplate.postForLocation(effectUrl, workflowParams, String.class);
                } catch (Exception e) {
                    // 简单记录日志或不处理
                    log.error("异步请求失败: {}", e.getMessage());
                }
            });
            log.info("Start effect workflow by Prefect API, workflowInstanceId: {}", workflowInstanceId);
        } catch (Exception e) {
            log.error("Error starting effect workflow by Prefect API: {}", e.getMessage());
            e.printStackTrace();
        }
    }

    public Map<String, Object> getWorkflowParams(WorkflowInstance workflowInstance, List<Video> videoList, int dramaType, String lang) {
        List<Video> videosToSave = Lists.newArrayList();
        List<VideoDto> videoDtoList = Lists.newArrayList();
        for (Video video : videoList) {
            video.setStatus(VideoStatusEnum.PROCESSING.getCode());
            video.setDramaType(dramaType);
            video.setLang(lang);
            videosToSave.add(video);

            VideoDto videoDto = VideoDto.builder()
                    .id(video.getId())
                    .uid(video.getUid())
                    .folder_id(video.getFolderId())
                    .folder_name(video.getFolderName())
                    .name(video.getName())
                    .object_key(video.getObjectKey())
                    .drama_type(video.getDramaType())
                    .lang(video.getLang())
                    .status(video.getStatus()).build();

            videoDtoList.add(videoDto);
        }
        videoRepository.saveAll(videosToSave);

        String pathName = WorkflowService.getWorkflowOutputPath(workflowInstance);
        String presetFilenames = workflowInstance.getPresetFilenames();
        List<String> presetFilenamesArr = new ArrayList<>();
        if (StringUtils.isNotEmpty(presetFilenames)) {
            presetFilenamesArr = JsonUtils.toObject(presetFilenames, new TypeReference<>() {
            });
        }

        Map<String, Object> res = new HashMap<>();
        res.put("workflow_instance_id", workflowInstance.getWorkflowInstanceId());
        res.put("videos", videoDtoList);
        res.put("path_name", pathName);
        res.put("preset_filenames", presetFilenamesArr);
        res.put("is_force", false);
        res.put("is_analysis", false);
        return res;
    }

    public Map<String, Object> getEffectWorkflowParams(WorkflowInstance workflowInstance) {
        String params = workflowInstance.getParams();
        FolderController.AnalysisFolderRequest request = JsonUtils.toObject(params, FolderController.AnalysisFolderRequest.class);

        String folderId = request.getFolderId();
        String uid = workflowInstance.getUid();
        List<Video> videoList = videoRepository.findByFolderIdAndUid(folderId, uid);

        String folderName = request.getFolderName();
        String folderPath = getFolderPath(uid, FolderTypeEnum.SHORT_DRAMA, folderName);
        List<String> s3ObjectKeys = s3Service.getS3ObjectKeys(folderPath);

        List<VideoDto> videoDtoList = Lists.newArrayList();
        for (Video video : videoList) {
            //排除不存在的文件
            if (s3ObjectKeys.contains(video.getObjectKey())) {
                VideoDto videoDto = VideoDto.builder()
                        .id(video.getId())
                        .uid(video.getUid())
                        .folder_id(video.getFolderId())
                        .folder_name(video.getFolderName())
                        .name(video.getName())
                        .object_key(video.getObjectKey())
                        .drama_type(video.getDramaType())
                        .lang(video.getLang())
                        .status(video.getStatus()).build();

                videoDtoList.add(videoDto);
            }
        }

        // 对videoDtoList进行排序
        videoDtoList = VideoSortUtils.sortByObjectKeyNumber(videoDtoList);

        String pathName = WorkflowService.getWorkflowOutputPath(workflowInstance);
        String presetFilenames = workflowInstance.getPresetFilenames();
        List<String> presetFilenamesArr = new ArrayList<>();
        if (StringUtils.isNotEmpty(presetFilenames)) {
            presetFilenamesArr = JsonUtils.toObject(presetFilenames, new TypeReference<>() {
            });
        }

        Map<String, Object> res = new HashMap<>();
        res.put("workflow_instance_id", workflowInstance.getWorkflowInstanceId());
        res.put("videos", videoDtoList);
        res.put("path_name", pathName);
        res.put("preset_filenames", presetFilenamesArr);
        res.put("is_force", false);
        res.put("is_analysis", false);
        return res;
    }

    public Map<String, Object> getWorkflowParams(String workflowInstanceId) {
        WorkflowInstance workflowInstance = workflowInstanceRepository.findByWorkflowInstanceId(workflowInstanceId)
                .orElseThrow(() -> new RuntimeException("error"));

        String params = workflowInstance.getParams();
        ConfigTemplate config = JsonUtils.toObject(params, ConfigTemplate.class);
        String videoListStr = config.getValueByKey("videoList");
        String uid = RequestContext.getCurrentUser();

        List<Folder> folders = folderRepository.findByUidAndTypeOrderByCreatedAtDesc(uid, FolderTypeEnum.SHORT_DRAMA);

        Map<String, Folder> folderMap = folders.stream()
                .collect(Collectors.toMap(
                        Folder::getName, // Key: folderName
                        folder -> folder,      // Value: Folder 对象本身
                        (existing, replacement) -> existing // 如果有重复 key，保留已存在的
                ));

        if (!folderMap.containsKey("default")) {
            Folder folder = Folder.builder()
                    .uid(uid)
                    .type(FolderTypeEnum.SHORT_DRAMA)
                    .name("default")
                    .build();
            folder = folderRepository.save(folder);
            folderMap.put("default", folder);
        }

        List<VideoDto> videoDtoList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(videoListStr)) {
            List<String> videos = JsonUtils.toObject(videoListStr, new TypeReference<>() {
            });

            if (CollectionUtils.isNotEmpty(videos)) {
                for (String objectKey : videos) {
                    if (StringUtils.isNotEmpty(objectKey)) {
                        log.info("objectKey: {}", objectKey);
                        String folderName = WorkflowHelper.getLastDirectoryFromPath(objectKey);
                        Folder folder = folderMap.get(folderName);

                        Optional<Video> videoOptional = videoRepository.findByObjectKeyAndUid(objectKey, uid);
                        Video video = null;
                        if (!videoOptional.isPresent()) {
                            String presignedUrl = s3Service.generatePresignedDownloadUrl(objectKey, 10);
                            double duration = videoService.getVideoDurationWithRetry(presignedUrl);
                            String name = WorkflowHelper.getFileNameFromPath(objectKey);
                            video = Video.builder()
                                    .uid(uid)
                                    .folderId(String.valueOf(folder.getId()))
                                    .folderName(folder.getName())
                                    .name(name)
                                    .objectKey(objectKey)
                                    .dramaType(Integer.parseInt(config.getValueByKey("dramaType")))
                                    .lang(config.getValueByKey("lang"))
                                    .status(VideoStatusEnum.INIT.getCode())
                                    .duration(duration)
                                    .build();
                        } else {
                            video = videoOptional.get();
                            video.setDramaType(Integer.parseInt(config.getValueByKey("dramaType")));
                            video.setLang(config.getValueByKey("lang"));
                        }
                        video = videoRepository.save(video);

                        VideoDto videoDto = VideoDto.builder()
                                .id(video.getId())
                                .uid(video.getUid())
                                .folder_id(video.getFolderId())
                                .folder_name(video.getFolderName())
                                .name(video.getName())
                                .object_key(video.getObjectKey())
                                .drama_type(video.getDramaType())
                                .lang(video.getLang())
                                .status(video.getStatus()).build();

                        videoDtoList.add(videoDto);
                    }
                }
            }
        }

        String pathName = WorkflowService.getWorkflowOutputPath(workflowInstance);
        String presetFilenames = workflowInstance.getPresetFilenames();
        List<String> presetFilenamesArr = new ArrayList<>();
        if (StringUtils.isNotEmpty(presetFilenames)) {
            presetFilenamesArr = JsonUtils.toObject(presetFilenames, new TypeReference<>() {
            });
        }

        Map<String, Object> res = new HashMap<>();
        res.put("workflow_instance_id", workflowInstanceId);
        res.put("videos", videoDtoList);
        res.put("path_name", pathName);
        res.put("preset_filenames", presetFilenamesArr);
        res.put("is_force", false);
        res.put("is_analysis", false);
        return res;
    }

    @Transactional
    public void updateHighlight(JsonNode jsonNode) {
        try {
            long videoId = jsonNode.get("video_id").asLong();
            Optional<Video> videoOptional = videoRepository.findById(videoId);
            if (videoOptional.isEmpty()) {
                return;
            }
            String uid = jsonNode.get("uid").asText();
            int type = jsonNode.get("type").asInt();
            Highlight highlight = highlightRepository.findByUidAndVideoIdAndType(uid, videoId, type);
            String dataStr = jsonNode.get("data").toString();
            if (highlight != null) {
                highlight.setData(dataStr);
            } else {
                highlight = Highlight.builder()
                        .videoId(videoId)
                        .uid(uid)
                        .type(type)
                        .data(dataStr)
                        .createdAt(System.currentTimeMillis())
                        .status(0)
                        .build();
            }

            highlightRepository.save(highlight);
        } catch (Exception e) {
            log.error("error", e);
            e.printStackTrace();
        }
    }

    @Transactional
    public void startAcousticWorkflow(Map<String, Object> res) {
        try {
            String workflowInstanceId = (String) res.get("workflow_instance_id");
            restTemplate.postForLocation(acousticUrl, res, String.class);
            log.info("Start acoustic workflow by Prefect API, workflowInstanceId: {}", workflowInstanceId);
        } catch (Exception e) {
            log.error("Error starting acoustic workflow by Prefect API: {}", e.getMessage());
            e.printStackTrace();
        }
    }

    private String getFolderPath(String uid, FolderTypeEnum folderType, String folderName) {
        return String.format("users/%s/assets/%s/%s/",
                uid,
                folderType.getName().toLowerCase(),
                folderName);
    }
}
