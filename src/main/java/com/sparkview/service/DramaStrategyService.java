package com.sparkview.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.sparkview.common.ConfigTemplate;
import com.sparkview.common.JsonUtils;
import com.sparkview.common.WorkflowInstanceStatus;
import com.sparkview.common.enums.HighlightEnum;
import com.sparkview.common.enums.ScoreGradeEnum;
import com.sparkview.common.enums.VideoDurationPatternEnum;
import com.sparkview.controller.dto.VideoDto;
import com.sparkview.persistence.ClipRecord;
import com.sparkview.persistence.Effects;
import com.sparkview.persistence.Highlight;
import com.sparkview.repository.EffectsRepository;
import com.sparkview.repository.HighlightRepository;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class DramaStrategyService {


    @Resource
    private EffectsRepository effectsRepository;
    @Resource
    private HighlightRepository highlightRepository;
    @Resource
    private ClipRecordService clipRecordService;
    @Resource
    private WorkflowService workflowService;


    public List<List<VideoDto>> getClipAssetsByDefaultStrategy(String uid, String workflowInstanceId, ConfigTemplate config, List<VideoDto> videos) {
        if (CollectionUtils.isEmpty(videos)) {
            log.error("videoDtoList is empty. uid: {}, workflowInstanceId: {}, config: {}", uid, workflowInstanceId, JsonUtils.toJson(config));
            workflowService.updateWorkflowInstanceStatusWithError(workflowInstanceId, WorkflowInstanceStatus.FAIL, "videoDtoList is empty");
            throw new RuntimeException("videoDurationPatternType is null");
        }

        String videoDurationValue = config.getValueByKey("videoDuration");
        VideoDurationPatternEnum videoDurationPatternType = VideoDurationPatternEnum.fromValue(Integer.parseInt(videoDurationValue));

        if (videoDurationPatternType == null) {
            log.error("videoDurationPatternType is null. uid: {}, workflowInstanceId: {}", uid, workflowInstanceId);
            workflowService.updateWorkflowInstanceStatusWithError(workflowInstanceId, WorkflowInstanceStatus.FAIL, "不支持的时长类型");
            throw new RuntimeException("videoDurationPatternType is null");
        }

        // 获取视频总时长
        double totalDuration = getDuration(videos);
        // 校验视频总时长和videoDurationPatternType是否匹配
        checkTotalDuration(videoDurationPatternType, totalDuration, workflowInstanceId);
        // 生成视频策略
        List<TargetTypeStrategy> targetTypeStrategeList = selectTargetTypeQuantity(videoDurationPatternType, totalDuration, workflowInstanceId);
        // 之前逻辑已经取到了第一个视频的开始高光时间点、最后一个视频的高光时间点以及中间连续视频的基本信息, 现在对中间视频进行后置处理片头片尾特效剔除
        setEffectCutTime(uid, Collections.singletonList(videos), workflowInstanceId);
        // 遍历视频策略获取对应的高光时刻
        return selectHighlightFragment(targetTypeStrategeList, videos, totalDuration, uid, workflowInstanceId);
    }

    /**
     * 校验视频总时长和videoDurationPatternType是否匹配
     */
    private void checkTotalDuration(VideoDurationPatternEnum videoDurationPatternType, Double totalDuration, String workflowInstanceId) {
        // 分转秒
        double minRequiredDuration = (double) videoDurationPatternType.getMinRequiredDuration() * 60;

        if (compareWithBigDecimal(totalDuration, minRequiredDuration) >= 0) {
            log.info("total duration is greater than minimum required duration. Meet the requirements. workflowInstanceId: {}, totalDuration: {}, minRequiredDuration: {}", workflowInstanceId, totalDuration, minRequiredDuration);
        } else {
            log.error("total duration is less than minimum required duration. Meet the requirements. workflowInstanceId: {}, totalDuration: {}, minRequiredDuration: {}", workflowInstanceId, totalDuration, minRequiredDuration);
            workflowService.updateWorkflowInstanceStatusWithError(workflowInstanceId, WorkflowInstanceStatus.FAIL, "视频时长小于" + minRequiredDuration + "s");
            throw new RuntimeException("total duration not meet minimum required duration. workflowInstanceId: " + workflowInstanceId + ", totalDuration: " + totalDuration);
        }
    }

    /**
     * 获取总视频时长
     */
    private double getDuration(List<VideoDto> videos) {
        BigDecimal bigDecimal = new BigDecimal("0");

        for (VideoDto video : videos) {
            String duration = String.valueOf(video.getDuration());
            bigDecimal = bigDecimal.add(new BigDecimal(duration));

        }

        return bigDecimal.doubleValue();
    }

    /**
     * 现在要求生产3个视频, 其中2个A类, 1个B类
     *
     * @param videoDurationPatternType 用户选取的生产视频类型
     * @param totalDuration            连续剧集的总时长
     * @return 要生成的3个视频的策略, 比如 一个"2-5min && A类的", 一个"5-10min && B类的" 等
     */
    private List<TargetTypeStrategy> selectTargetTypeQuantity(VideoDurationPatternEnum videoDurationPatternType, double totalDuration, String workflowInstanceId) {
        // 当用户选取自动，则根据用户点击生成按钮时，连续剧集总时长，以计算生成视频的市场策略，具体策略是
        // - 剧集时长≥30分钟，则2-5分钟、5-10分钟、10-20分钟，各1个
        // - 30＞剧集时长≥15，则5-10分钟 2个，2-5分钟 1个
        // - 15＞剧集时长≥8，则2-5分钟 3个

        // 其他选中的现在都是默认3个
        List<TargetTypeStrategy> result = new ArrayList<>();

        switch (videoDurationPatternType) {
            case AUTO:
                if (compareWithBigDecimal(totalDuration, 20 * 60) >= 0) {
                    result.add(new TargetTypeStrategy(VideoDurationPatternEnum.SECOND_VIDEO, ScoreGradeEnum.A));
                    result.add(new TargetTypeStrategy(VideoDurationPatternEnum.FIVE_VIDEO, ScoreGradeEnum.A));
                    result.add(new TargetTypeStrategy(VideoDurationPatternEnum.TEN_VIDEO, ScoreGradeEnum.B));
                }

                if (compareWithBigDecimal(totalDuration, 10 * 60) >= 0 && compareWithBigDecimal(totalDuration, 20 * 60) < 0) {
                    result.add(new TargetTypeStrategy(VideoDurationPatternEnum.SECOND_VIDEO, ScoreGradeEnum.A));
                    result.add(new TargetTypeStrategy(VideoDurationPatternEnum.FIVE_VIDEO, ScoreGradeEnum.A));
                    result.add(new TargetTypeStrategy(VideoDurationPatternEnum.FIVE_VIDEO, ScoreGradeEnum.B));
                }

                if (compareWithBigDecimal(totalDuration, 5 * 60) >= 0 && compareWithBigDecimal(totalDuration, 10 * 60) < 0) {
                    result.add(new TargetTypeStrategy(VideoDurationPatternEnum.SECOND_VIDEO, ScoreGradeEnum.A));
                    result.add(new TargetTypeStrategy(VideoDurationPatternEnum.SECOND_VIDEO, ScoreGradeEnum.A));
                    result.add(new TargetTypeStrategy(VideoDurationPatternEnum.SECOND_VIDEO, ScoreGradeEnum.B));
                }

                break;
            case SECOND_VIDEO:
                result.add(new TargetTypeStrategy(VideoDurationPatternEnum.SECOND_VIDEO, ScoreGradeEnum.A));
                result.add(new TargetTypeStrategy(VideoDurationPatternEnum.SECOND_VIDEO, ScoreGradeEnum.A));
                result.add(new TargetTypeStrategy(VideoDurationPatternEnum.SECOND_VIDEO, ScoreGradeEnum.B));
                break;
            case FIVE_VIDEO:
                result.add(new TargetTypeStrategy(VideoDurationPatternEnum.FIVE_VIDEO, ScoreGradeEnum.A));
                result.add(new TargetTypeStrategy(VideoDurationPatternEnum.FIVE_VIDEO, ScoreGradeEnum.A));
                result.add(new TargetTypeStrategy(VideoDurationPatternEnum.FIVE_VIDEO, ScoreGradeEnum.B));
                break;
            case TEN_VIDEO:
                result.add(new TargetTypeStrategy(VideoDurationPatternEnum.TEN_VIDEO, ScoreGradeEnum.A));
                result.add(new TargetTypeStrategy(VideoDurationPatternEnum.TEN_VIDEO, ScoreGradeEnum.A));
                result.add(new TargetTypeStrategy(VideoDurationPatternEnum.TEN_VIDEO, ScoreGradeEnum.B));
                break;
            default:
        }

        log.info("selected video strategy: {}. target type list: {}, workflowInstanceId: {}", videoDurationPatternType, result, workflowInstanceId);
        return result;
    }

    /**
     * 比较大小
     */
    public static int compareWithBigDecimal(double a, double b) {
        // 必须使用字符串构造 BigDecimal，避免直接传入 double 导致精度丢失:ml-citation{ref="4,7" data="citationList"}
        BigDecimal num1 = new BigDecimal(Double.toString(a));
        BigDecimal num2 = new BigDecimal(Double.toString(b));
        return num1.compareTo(num2); // 返回 -1/0/1 表示大小关系
    }

    /**
     * - 2-5分钟，对应，3分钟
     * - 5-10分钟，对应，8分钟
     * - 10-20分钟，对应，15分钟
     * 通过指定策略选取高光时刻
     */
    private List<List<VideoDto>> selectHighlightFragment(List<TargetTypeStrategy> targetTypeStrategeList, List<VideoDto> videos, double totalDuration, String uid, String workflowInstanceId) {
        Map<Long, List<HighlightCandidate>> highlightMap = getHighlightMap(uid, workflowInstanceId, videos);

        if (highlightMap.isEmpty()) {
            log.error("highlight map is null or empty, uid: {}, workflowInstanceId: {}", uid, workflowInstanceId);
            workflowService.updateWorkflowInstanceStatusWithError(workflowInstanceId, WorkflowInstanceStatus.FAIL, "未查询到高光数据");
            throw new RuntimeException("highlight map is null or empty, uid: " + uid + ", workflowInstanceId: " + workflowInstanceId);
        }

        List<List<VideoDto>> result = new ArrayList<>();
        Set<String> highlightUniqueSet = new HashSet<>();
        // 已经生成的去重
        Set<String> duplicateInDb = findDuplicateInDb(uid, videos);

        if (CollectionUtils.isNotEmpty(duplicateInDb)) {
            highlightUniqueSet.addAll(duplicateInDb);
        }

        int index = 1;
        // 选择随机视频使用，已经用过的视频会缓存到这里
        Set<Long> curCacheUsed = new HashSet<>();

        for (TargetTypeStrategy targetTypeStrategy : targetTypeStrategeList) {
            String cur_workflowInstanceId_index = workflowInstanceId + "_" + index;
            log.info(">>>>> start exe cur_workflowInstanceId_index: {}", cur_workflowInstanceId_index);
            List<VideoDto> perStrategyList = new ArrayList<>();
            Integer backwardTime = VideoDurationPatternEnum.getBackwardTimeBy(targetTypeStrategy.videoDurationPatternType);

            if (backwardTime == null) {
                log.error("backward time is null, targetTypeStrategy: {}, uid: {}, workflowInstanceId_index: {}", targetTypeStrategy, uid, cur_workflowInstanceId_index);
                workflowService.updateWorkflowInstanceStatusWithError(workflowInstanceId, WorkflowInstanceStatus.FAIL, "未设置从连续视频尾部倒推时间点数值");
                throw new RuntimeException("backward time is null, uid: " + uid + ", workflowInstanceId_index: " + cur_workflowInstanceId_index);
            }

            // 计算开头高光选取时间范围时间, 开始默认第一个视频的0位置
            String startHighlightSelectRangeEndTime = new BigDecimal(String.valueOf(totalDuration)).subtract(new BigDecimal(String.valueOf(backwardTime * 60))).toString();
            String endHighlightSelectRangeStartTime = null;
            String endHighlightSelectRangeEndTime = null;
            // 选高光, 拼接视频
            // 累计已处理的连续视频时长, 不包括正在处理的
            BigDecimal currentTotalDuration = new BigDecimal("0");
            // 当前策略下找到的开始位置的高光时间点
            String highlightStartTime = null;
            // 随机选定一个视频位置
            Long randomVideoId = selectRandomVideoId(videos, startHighlightSelectRangeEndTime, curCacheUsed);

            if (targetTypeStrategy.scoreGrade == ScoreGradeEnum.A) {
                boolean findStart = false;
                boolean complete = false;
                // 找到随机制定的起始视频
                boolean findStartRandomVideoId = false;

                for (VideoDto videoDto : videos) {
                    if (!findStartRandomVideoId){
                        // 如果不相等, 继续顺序查找
                        if(videoDto.getId().compareTo(randomVideoId) != 0){
                            continue;
                        }else {
                            findStartRandomVideoId = true;
                            // 执行下后续操作
                        }
                    }

                    if (complete) {
                        break;
                    }

                    // 找开头
                    if (!findStart) {
                        // 如果上一个视频已经超过了可查询开头高光的范围
                        if (compareWithBigDecimal(currentTotalDuration.doubleValue(), new BigDecimal(startHighlightSelectRangeEndTime).doubleValue()) >= 0) {
                            // 超过允许范围, 不记录中间连续list, 跳出
                            log.error("typeA greater than startHighlightSelectRangeEndTime, but also no find start highlight. strategy: {}, uid: {}, workflowInstanceId_index: {}, currentTotalDuration: {}, startHighlightSelectRangeEndTime: {}",
                                    targetTypeStrategy, uid, cur_workflowInstanceId_index, currentTotalDuration.doubleValue(), startHighlightSelectRangeEndTime);
                            break;
                        }

                        List<HighlightCandidate> highlightCandidates = highlightMap.get(videoDto.getId());

                        if (highlightCandidates == null || highlightCandidates.isEmpty()) {
                            log.info("typeA find start skip. find next. highlightCandidates is empty, videoId: {}, cur_workflowInstanceId_index: {}", videoDto.getId(), cur_workflowInstanceId_index);
                            // 统计连续视频累计时长, 后置计算
                            currentTotalDuration = currentTotalDuration.add(new BigDecimal(String.valueOf(videoDto.getDuration())));
                            continue;
                        }

                        // 打乱顺序
                        Collections.shuffle(highlightCandidates);
                        log.info("typeA find start. videoId: {}, highlightCandidates size: {}", videoDto.getId(), highlightCandidates.size());

                        // 在当前视频总找满足条件的高光
                        for (HighlightCandidate highlightCandidate : highlightCandidates) {
                            // 计算高光时刻的时间点
                            double highlightTime = currentTotalDuration.add(new BigDecimal(String.valueOf(highlightCandidate.getStartTime()))).doubleValue();
                            // 检验高光是否在允许范围内
                            if (compareWithBigDecimal(highlightTime, new BigDecimal(startHighlightSelectRangeEndTime).doubleValue()) > 0) {
                                log.info("typeA find start. find next. out of startTime range skip. videoId: {}, current highlight startTime: {}, compute highlight time: {}, startHighlightSelectRangeEndTime: {}, cur_workflowInstanceId_index: {}",
                                        videoDto.getId(), highlightCandidate.getStartTime(), highlightTime, startHighlightSelectRangeEndTime, cur_workflowInstanceId_index);
                                // 超过允许范围, 找下一个
                                continue;
                            }

                            // 1. 视觉都是高光A类
                            if (highlightCandidate.source == HighlightEnum.Vision) {
                                // Vision类高光
                                // 该HighlightTime，对应的  音效片段的StartTime 作为剪辑视频开场时间点
                                VideoDto tempVideoDto = new VideoDto();
                                BeanUtils.copyProperties(videoDto, tempVideoDto);
                                // 找对应的音效片段
                                String startTime = findStartAcousticByVision(highlightCandidate, highlightCandidates, cur_workflowInstanceId_index);

                                if (StringUtils.isBlank(startTime)) {
                                    // 无对应音频, 跳过
                                    log.info("typeA find start. no find acoustic same. skip for videoId: {}, highlight type: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                            videoDto.getId(), HighlightEnum.Vision, JsonUtils.toJson(highlightCandidate), cur_workflowInstanceId_index, targetTypeStrategy);
                                    continue;
                                }

                                // 如果尾部有定格时间
                                if (StringUtils.isNotBlank(tempVideoDto.getEndTime())) {
                                    // 如果尾部定格时间在当前高光时间之前, 则跳过
                                    if (compareWithBigDecimal(Double.parseDouble(tempVideoDto.getEndTime()), Double.parseDouble(startTime)) <= 0) {
                                        // 无对应音频, 跳过
                                        log.info("typeA find start. effect endTime before cur find startTime. skip for videoId: {}, highlight type: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                                videoDto.getId(), HighlightEnum.Vision, JsonUtils.toJson(highlightCandidate), cur_workflowInstanceId_index, targetTypeStrategy);
                                        continue;
                                    }
                                }

                                tempVideoDto.setStartTime(startTime);
                                // 去重高光
                                String unique = videoDto.getId() + "_" + tempVideoDto.getStartTime();

                                if (highlightUniqueSet.contains(unique)) {
                                    log.info("typeA find start. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Vision, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                    continue;
                                }

                                perStrategyList.add(tempVideoDto);
                                // 找到跳出
                                findStart = true;
                                highlightStartTime = currentTotalDuration.add(new BigDecimal(tempVideoDto.getStartTime())).toString();
                                addUniqueKey(highlightUniqueSet, unique);
                                log.info("typeA find drama startTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                        HighlightEnum.Vision, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                break;
                            }

                            // 2. TextOver Clip Score识别出的8分以上的 为A类
                            if (highlightCandidate.source == HighlightEnum.Textover) {
                                // 得分不满足, 跳过
                                if (compareWithBigDecimal(highlightCandidate.getPriorityScore(), 8.0) < 0) {
                                    log.info("typeA find start time. not meet score. skip. videoId: {}, highlight type: {}, cur score: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                            videoDto.getId(), HighlightEnum.Textover, highlightCandidate.getPriorityScore(), highlightCandidate, cur_workflowInstanceId_index, targetTypeStrategy);
                                    continue;
                                }

                                // TextOver类高光
                                // 该片段StartTime，直接作为剪辑视频开场时间点即可 作为剪辑视频开场时间点
                                VideoDto tempVideoDto = new VideoDto();
                                BeanUtils.copyProperties(videoDto, tempVideoDto);
                                String startTime = highlightCandidate.startTime;

                                // 如果尾部有定格时间
                                if (StringUtils.isNotBlank(tempVideoDto.getEndTime())) {
                                    // 如果尾部定格时间在当前高光时间之前, 则跳过
                                    if (compareWithBigDecimal(Double.parseDouble(tempVideoDto.getEndTime()), Double.parseDouble(startTime)) <= 0) {
                                        // 无对应音频, 跳过
                                        log.info("typeA find start. effect endTime before cur find startTime. skip for videoId: {}, highlight type: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                                videoDto.getId(), HighlightEnum.Textover, JsonUtils.toJson(highlightCandidate), cur_workflowInstanceId_index, targetTypeStrategy);
                                        continue;
                                    }
                                }

                                tempVideoDto.setStartTime(startTime);
                                // 去重高光
                                String unique = videoDto.getId() + "_" + tempVideoDto.getStartTime();

                                if (highlightUniqueSet.contains(unique)) {
                                    log.info("typeA find start. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Textover, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                    continue;
                                }

                                perStrategyList.add(tempVideoDto);
                                // 找到跳出
                                findStart = true;
                                highlightStartTime = currentTotalDuration.add(new BigDecimal(tempVideoDto.getStartTime())).toString();
                                addUniqueKey(highlightUniqueSet, unique);
                                log.info("typeA find drama startTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                        HighlightEnum.Textover, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                break;
                            }

                            // 3. Acoustic Clip Score识别出的8.5分 为A类
                            if (highlightCandidate.source == HighlightEnum.Acoustic) {
                                // 得分不满足, 跳过
                                if (compareWithBigDecimal(highlightCandidate.getPriorityScore(), 8.5) < 0) {
                                    log.info("typeA find startTime. not meet score. skip. videoId: {}, highlight type: {}, cur score: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                            videoDto.getId(), HighlightEnum.Acoustic, highlightCandidate.getPriorityScore(), highlightCandidate, cur_workflowInstanceId_index, targetTypeStrategy);
                                    continue;
                                }

                                // TextOver类高光
                                // 该片段StartTime，直接作为剪辑视频开场时间点即可 作为剪辑视频开场时间点
                                VideoDto tempVideoDto = new VideoDto();
                                BeanUtils.copyProperties(videoDto, tempVideoDto);
                                String startTime = highlightCandidate.startTime;

                                // 如果尾部有定格时间
                                if (StringUtils.isNotBlank(tempVideoDto.getEndTime())) {
                                    // 如果尾部定格时间在当前高光时间之前, 则跳过
                                    if (compareWithBigDecimal(Double.parseDouble(tempVideoDto.getEndTime()), Double.parseDouble(startTime)) <= 0) {
                                        // 无对应音频, 跳过
                                        log.info("typeA find start. effect endTime before cur find startTime. skip for videoId: {}, highlight type: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                                videoDto.getId(), HighlightEnum.Acoustic, JsonUtils.toJson(highlightCandidate), cur_workflowInstanceId_index, targetTypeStrategy);
                                        continue;
                                    }
                                }

                                tempVideoDto.setStartTime(startTime);
                                // 去重高光
                                String unique = videoDto.getId() + "_" + tempVideoDto.getStartTime();

                                if (highlightUniqueSet.contains(unique)) {
                                    log.info("typeA find start. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Acoustic, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                    continue;
                                }

                                perStrategyList.add(tempVideoDto);
                                // 找到跳出
                                findStart = true;
                                highlightStartTime = currentTotalDuration.add(new BigDecimal(tempVideoDto.getStartTime())).toString();
                                addUniqueKey(highlightUniqueSet, unique);
                                log.info("typeA find drama startTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                        HighlightEnum.Acoustic, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                break;
                            }
                        }

                        // 如果找到高光视频, 需要检验尾部找视频的时间范围是否还在这个视频里
                        if (findStart) {
                            double rangeStart = targetTypeStrategy.getVideoDurationPatternType().getRangeStart() * 60;
                            double rangeEnd = targetTypeStrategy.getVideoDurationPatternType().getRangeEnd() * 60;
                            endHighlightSelectRangeStartTime = new BigDecimal(highlightStartTime).add(new BigDecimal(String.valueOf(rangeStart))).toString();
                            endHighlightSelectRangeEndTime = new BigDecimal(highlightStartTime).add(new BigDecimal(String.valueOf(rangeEnd))).toString();

                            // 在当前视频总找满足条件的高光
                            for (HighlightCandidate highlightCandidate : highlightCandidates) {
                                // 计算高光时刻的时间点
                                double highlightTime = currentTotalDuration.add(new BigDecimal(String.valueOf(highlightCandidate.getStartTime()))).doubleValue();
                                // 检验高光是否在允许范围内
                                if (compareWithBigDecimal(highlightTime, new BigDecimal(endHighlightSelectRangeStartTime).doubleValue()) < 0 || compareWithBigDecimal(highlightTime, new BigDecimal(endHighlightSelectRangeEndTime).doubleValue()) > 0) {
                                    log.info("typeA find end. out of endTime range skip. videoId: {}, current highlight startTime: {}, compute highlight time: {}, startHighlightSelectRangeEndTime: {}, cur_workflowInstanceId_index: {}",
                                            videoDto.getId(), highlightCandidate.getStartTime(), highlightTime, startHighlightSelectRangeEndTime, cur_workflowInstanceId_index);
                                    // 超过允许范围, 找下一个
                                    continue;
                                }

                                // 1. 视觉都是高光A类
                                if (highlightCandidate.source == HighlightEnum.Vision) {
                                    // 结尾Vision类高光
                                    //     - 【判断】Vision HighlightTime的时间点  是否 落在某个Textover类或Acoustic类的高光片段中
                                    //      - 均否，则用HighlightTime的StartTime作为开场时间点
                                    //      - Textover是，或，Acoustic是，则用  是的那个  的EndTime作为开场时间点（比如Textover是，则用Textover的EndTime作为开场时间点）
                                    //      - 均是，则用Textover的EndTime为开场时间点
                                    // 找对应的音效片段
                                    String endTime = findEndAcousticOrTextOverByVision(highlightCandidate, highlightCandidates, cur_workflowInstanceId_index);

                                    if (StringUtils.isBlank(endTime)) {
                                        // 无对应音频, 跳过
                                        log.info("typeA no find acoustic or textOver start time for videoId: {}, highlight type: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                                videoDto.getId(), HighlightEnum.Vision, JsonUtils.toJson(highlightCandidate), cur_workflowInstanceId_index, targetTypeStrategy);
                                        continue;
                                    }

                                    String curEndTime = StringUtils.isBlank(endTime) ? highlightCandidate.startTime : endTime;
                                    // 去重高光
                                    String unique = videoDto.getId() + "_" + curEndTime;

                                    if (highlightUniqueSet.contains(unique)) {
                                        log.info("typeA find end. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Vision, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                        continue;
                                    }

                                    VideoDto tempVideoDto = perStrategyList.get(perStrategyList.size() - 1);
                                    tempVideoDto.setEndTime(curEndTime);
                                    // 找到跳出
                                    complete = true;
                                    addUniqueKey(highlightUniqueSet, unique);
                                    log.info("typeA find drama endTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                            HighlightEnum.Vision, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                    break;
                                }

                                // 2. Textover Clip Score识别出的8分以上的 为A类
                                if (highlightCandidate.source == HighlightEnum.Textover) {
                                    // 得分不满足, 跳过
                                    if (compareWithBigDecimal(highlightCandidate.getPriorityScore(), 8.0) < 0) {
                                        log.info("typeA find end time. not meet score. skip. videoId: {}, highlight type: {}, cur score: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                                videoDto.getId(), HighlightEnum.Textover, highlightCandidate.getPriorityScore(), highlightCandidate, cur_workflowInstanceId_index, targetTypeStrategy);
                                        continue;
                                    }

                                    // TextOver类高光
                                    //  - 该片段EndTime，直接作为剪辑视频结尾点时间点即可
                                    // 去重高光
                                    String unique = videoDto.getId() + "_" + highlightCandidate.getEndTime();

                                    if (highlightUniqueSet.contains(unique)) {
                                        log.info("typeA find end. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Textover, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                        continue;
                                    }

                                    VideoDto tempVideoDto = perStrategyList.get(perStrategyList.size() - 1);
                                    tempVideoDto.setEndTime(highlightCandidate.endTime);
                                    // 找到跳出
                                    complete = true;
                                    addUniqueKey(highlightUniqueSet, unique);
                                    log.info("typeA find drama endTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                            HighlightEnum.Textover, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                    break;
                                }

                                // 3. Acoustic Clip Score识别出的8.5分 为A类
                                if (highlightCandidate.source == HighlightEnum.Acoustic) {
                                    // 得分不满足, 跳过
                                    if (compareWithBigDecimal(highlightCandidate.getPriorityScore(), 8.5) < 0) {
                                        log.info("typeA find endTime. not meet score. skip. videoId: {}, highlight type: {}, cur score: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                                videoDto.getId(), HighlightEnum.Acoustic, highlightCandidate.getPriorityScore(), highlightCandidate, cur_workflowInstanceId_index, targetTypeStrategy);
                                        continue;
                                    }

                                    // TextOver类高光
                                    //  - 该片段EndTime，直接作为剪辑视频结尾时间点即可
                                    // 去重高光
                                    String unique = videoDto.getId() + "_" + highlightCandidate.endTime;

                                    if (highlightUniqueSet.contains(unique)) {
                                        log.info("typeA find end. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Acoustic, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                        continue;
                                    }

                                    VideoDto tempVideoDto = perStrategyList.get(perStrategyList.size() - 1);
                                    tempVideoDto.setEndTime(highlightCandidate.endTime);
                                    // 找到跳出
                                    complete = true;
                                    addUniqueKey(highlightUniqueSet, unique);
                                    log.info("typeA find drama endTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                            HighlightEnum.Acoustic, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                    break;
                                }
                            }
                        }
                    } else {
                        // 如果上一个视频已经超过了可查询结尾高光的范围
                        if (compareWithBigDecimal(currentTotalDuration.doubleValue(), new BigDecimal(endHighlightSelectRangeEndTime).doubleValue()) >= 0) {
                            // 超过允许范围, 不记录中间连续list, 跳出报错
                            log.error("typeA find end. greater than endHighlightSelectRangeEndTime, but also no find end highlight. strategy: {}, uid: {}, currentTotalDuration: {}, endHighlightSelectRangeEndTime: {}, workflowInstanceId_index: {}",
                                    targetTypeStrategy, uid, currentTotalDuration, endHighlightSelectRangeEndTime, cur_workflowInstanceId_index);
                            break;
                        }

                        // 检验高光是否在允许范围内
                        double curTotalDuration = currentTotalDuration.add(new BigDecimal(String.valueOf(videoDto.getDuration()))).doubleValue();

                        if (compareWithBigDecimal(curTotalDuration, new BigDecimal(endHighlightSelectRangeStartTime).doubleValue()) < 0) {
                            log.info("typeA find end. curTotalDuration less than endHighlightSelectRangeStartTime. record middle video, find next video. videoId: {}, current video curTotalDuration: {}, endHighlightSelectRangeStartTime: {}, cur_workflowInstanceId_index: {}",
                                    videoDto.getId(), curTotalDuration, endHighlightSelectRangeStartTime, cur_workflowInstanceId_index);
                            // 超过允许范围, 记录中间连续list, 然后找下一个
                            VideoDto tempVideoDto = new VideoDto();
                            BeanUtils.copyProperties(videoDto, tempVideoDto);
                            perStrategyList.add(tempVideoDto);
                            // 统计连续视频累计时长, 后置计算
                            currentTotalDuration = currentTotalDuration.add(new BigDecimal(String.valueOf(videoDto.getDuration())));
                            continue;
                        }

                        List<HighlightCandidate> highlightCandidates = highlightMap.get(videoDto.getId());

                        if (highlightCandidates == null || highlightCandidates.isEmpty()) {
                            log.info("typeA find end skip. find next, highlightCandidates is empty, videoId: {}, cur_workflowInstanceId_index: {}", videoDto.getId(), cur_workflowInstanceId_index);
                            // 当前无高光, 记录中间连续list, 然后找下一个
                            VideoDto tempVideoDto = new VideoDto();
                            BeanUtils.copyProperties(videoDto, tempVideoDto);
                            perStrategyList.add(tempVideoDto);
                            // 统计连续视频累计时长, 后置计算
                            currentTotalDuration = currentTotalDuration.add(new BigDecimal(String.valueOf(videoDto.getDuration())));
                            continue;
                        }

                        log.info("typeA find end. videoId: {}, highlightCandidates size: {}", videoDto.getId(), highlightCandidates.size());

                        // 在当前视频总找满足条件的高光
                        for (HighlightCandidate highlightCandidate : highlightCandidates) {
                            // 计算高光时刻的时间点
                            double highlightTime = currentTotalDuration.add(new BigDecimal(String.valueOf(highlightCandidate.getStartTime()))).doubleValue();
                            // 检验高光是否在允许范围内
                            if (compareWithBigDecimal(highlightTime, new BigDecimal(endHighlightSelectRangeStartTime).doubleValue()) < 0 || compareWithBigDecimal(highlightTime, new BigDecimal(endHighlightSelectRangeEndTime).doubleValue()) > 0) {
                                log.info("typeA find end. find next. out of endTime range skip. videoId: {}, current highlight startTime: {}, compute highlight time: {}, endHighlightSelectRangeStartTime: {}, endHighlightSelectRangeEndTime: {}, cur_workflowInstanceId_index: {}",
                                        videoDto.getId(), highlightCandidate.getStartTime(), highlightTime, endHighlightSelectRangeStartTime, endHighlightSelectRangeEndTime, cur_workflowInstanceId_index);
                                // 超过允许范围, 找下一个
                                continue;
                            }

                            // 1. 视觉都是高光A类
                            if (highlightCandidate.source == HighlightEnum.Vision) {
                                // 结尾Vision类高光
                                //     - 【判断】Vision HighlightTime的时间点  是否 落在某个Textover类或Acoustic类的高光片段中
                                //      - 均否，则用HighlightTime的StartTime作为开场时间点
                                //      - Textover是，或，Acoustic是，则用  是的那个  的EndTime作为开场时间点（比如Textover是，则用Textover的EndTime作为开场时间点）
                                //      - 均是，则用Textover的EndTime为开场时间点
                                VideoDto tempVideoDto = new VideoDto();
                                BeanUtils.copyProperties(videoDto, tempVideoDto);
                                // 找对应的音效片段
                                String endTime = findEndAcousticOrTextOverByVision(highlightCandidate, highlightCandidates, cur_workflowInstanceId_index);

                                if (StringUtils.isBlank(endTime)) {
                                    // 无对应音频, 跳过
                                    log.info("typeA find end, no find acoustic or textOver same, skip for videoId: {}, highlight type: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                            videoDto.getId(), HighlightEnum.Vision, JsonUtils.toJson(highlightCandidate), cur_workflowInstanceId_index, targetTypeStrategy);
                                    continue;
                                }

                                tempVideoDto.setEndTime(StringUtils.isBlank(endTime) ? highlightCandidate.startTime : endTime);
                                // 去重高光
                                String unique = videoDto.getId() + "_" + tempVideoDto.getEndTime();

                                if (highlightUniqueSet.contains(unique)) {
                                    log.info("typeA find end. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Vision, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                    continue;
                                }

                                perStrategyList.add(tempVideoDto);
                                // 找到跳出
                                complete = true;
                                addUniqueKey(highlightUniqueSet, unique);
                                log.info("typeA find drama endTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                        HighlightEnum.Vision, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                break;
                            }

                            // 2. Textover Clip Score识别出的8分以上的 为A类
                            if (highlightCandidate.source == HighlightEnum.Textover) {
                                // 得分不满足, 跳过
                                if (compareWithBigDecimal(highlightCandidate.getPriorityScore(), 8.0) < 0) {
                                    log.info("typeA find end time. not meet score. skip. videoId: {}, highlight type: {}, cur score: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                            videoDto.getId(), HighlightEnum.Textover, highlightCandidate.getPriorityScore(), highlightCandidate, cur_workflowInstanceId_index, targetTypeStrategy);
                                    continue;
                                }

                                // TextOver类高光
                                //  - 该片段EndTime，直接作为剪辑视频结尾点时间点即可
                                VideoDto tempVideoDto = new VideoDto();
                                BeanUtils.copyProperties(videoDto, tempVideoDto);
                                tempVideoDto.setEndTime(highlightCandidate.endTime);
                                // 去重高光
                                String unique = videoDto.getId() + "_" + tempVideoDto.getEndTime();

                                if (highlightUniqueSet.contains(unique)) {
                                    log.info("typeA find end. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Textover, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                    continue;
                                }

                                perStrategyList.add(tempVideoDto);
                                // 找到跳出
                                complete = true;
                                addUniqueKey(highlightUniqueSet, unique);
                                log.info("typeA find drama startTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                        HighlightEnum.Textover, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                break;
                            }

                            // 3. Acoustic Clip Score识别出的8.5分 为A类
                            if (highlightCandidate.source == HighlightEnum.Acoustic) {
                                // 得分不满足, 跳过
                                if (compareWithBigDecimal(highlightCandidate.getPriorityScore(), 8.5) < 0) {
                                    log.info("typeA find endTime. not meet score. skip. videoId: {}, highlight type: {}, cur score: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                            videoDto.getId(), HighlightEnum.Acoustic, highlightCandidate.getPriorityScore(), highlightCandidate, cur_workflowInstanceId_index, targetTypeStrategy);
                                    continue;
                                }

                                // TextOver类高光
                                //  - 该片段EndTime，直接作为剪辑视频结尾时间点即可
                                VideoDto tempVideoDto = new VideoDto();
                                BeanUtils.copyProperties(videoDto, tempVideoDto);
                                tempVideoDto.setEndTime(highlightCandidate.endTime);
                                // 去重高光
                                String unique = videoDto.getId() + "_" + tempVideoDto.getEndTime();

                                if (highlightUniqueSet.contains(unique)) {
                                    log.info("typeA find end. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Acoustic, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                    continue;
                                }

                                perStrategyList.add(tempVideoDto);
                                // 找到跳出
                                complete = true;
                                addUniqueKey(highlightUniqueSet, unique);
                                log.info("typeA find drama endTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                        HighlightEnum.Acoustic, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                break;
                            }
                        }

                        // 在当前连续视频中未找到符合条件的高光, 记录连续视频
                        if (!complete) {
                            log.info("typeA find end. current video no complete find highlight. find next, videoId: {}, cur_workflowInstanceId_index: {}",
                                    videoDto.getId(), cur_workflowInstanceId_index);
                            // 当前无高光, 记录中间连续list, 然后找下一个
                            VideoDto tempVideoDto = new VideoDto();
                            BeanUtils.copyProperties(videoDto, tempVideoDto);
                            perStrategyList.add(tempVideoDto);
                            // 统计连续视频累计时长, 后置计算
                            currentTotalDuration = currentTotalDuration.add(new BigDecimal(String.valueOf(videoDto.getDuration())));
                            continue;
                        }
                    }

                    // 统计连续视频累计时长, 后置计算
                    currentTotalDuration = currentTotalDuration.add(new BigDecimal(String.valueOf(videoDto.getDuration())));
                }
            }

            // B类不找视觉的类型
            if (targetTypeStrategy.scoreGrade == ScoreGradeEnum.B) {
                boolean findStart = false;
                boolean complete = false;
                // 找到随机制定的起始视频
                boolean findStartRandomVideoId = false;

                for (VideoDto videoDto : videos) {
                    if (!findStartRandomVideoId){
                        // 如果不相等, 继续顺序查找
                        if(videoDto.getId().compareTo(randomVideoId) != 0){
                            continue;
                        }else {
                            findStartRandomVideoId = true;
                            // 执行下后续操作
                        }
                    }

                    if (complete) {
                        break;
                    }

                    // 找开头
                    if (!findStart) {
                        // 如果上一个视频已经超过了可查询开头高光的范围
                        if (compareWithBigDecimal(currentTotalDuration.doubleValue(), new BigDecimal(startHighlightSelectRangeEndTime).doubleValue()) >= 0) {
                            // 超过允许范围, 不记录中间连续list, 跳出
                            log.error("typeB greater than startHighlightSelectRangeEndTime, but also no find start highlight. strategy: {}, uid: {}, workflowInstanceId_index: {}, currentTotalDuration: {}, startHighlightSelectRangeEndTime: {}",
                                    targetTypeStrategy, uid, cur_workflowInstanceId_index, currentTotalDuration.doubleValue(), startHighlightSelectRangeEndTime);
                            break;
                        }

                        List<HighlightCandidate> highlightCandidates = highlightMap.get(videoDto.getId());

                        if (highlightCandidates == null || highlightCandidates.isEmpty()) {
                            log.info("typeB find start skip. find next. highlightCandidates is empty, videoId: {}, cur_workflowInstanceId_index: {}", videoDto.getId(), cur_workflowInstanceId_index);
                            // 统计连续视频累计时长, 后置计算
                            currentTotalDuration = currentTotalDuration.add(new BigDecimal(String.valueOf(videoDto.getDuration())));
                            continue;
                        }

                        // 打乱顺序
                        Collections.shuffle(highlightCandidates);
                        log.info("typeB find start. videoId: {}, highlightCandidates size: {}", videoDto.getId(), highlightCandidates.size());

                        // 在当前视频总找满足条件的高光
                        for (HighlightCandidate highlightCandidate : highlightCandidates) {
                            // 1. 视觉都是高光A类, B类跳过
                            if (highlightCandidate.source == HighlightEnum.Vision) {
                                continue;
                            }

                            // 计算高光时刻的时间点
                            double highlightTime = currentTotalDuration.add(new BigDecimal(String.valueOf(highlightCandidate.getStartTime()))).doubleValue();
                            // 检验高光是否在允许范围内
                            if (compareWithBigDecimal(highlightTime, new BigDecimal(startHighlightSelectRangeEndTime).doubleValue()) > 0) {
                                log.info("typeB find start. find next. out of startTime range skip. videoId: {}, current highlight startTime: {}, compute highlight time: {}, startHighlightSelectRangeEndTime: {}, cur_workflowInstanceId_index: {}",
                                        videoDto.getId(), highlightCandidate.getStartTime(), highlightTime, startHighlightSelectRangeEndTime, cur_workflowInstanceId_index);
                                // 超过允许范围, 找下一个
                                continue;
                            }

                            // 2. TextOver Clip Score识别出的8.5分＞Acoustic Clip≥7分  且  8>TextOver Clip ≥6
                            if (highlightCandidate.source == HighlightEnum.Textover) {
                                // 得分不满足, 跳过
                                if (compareWithBigDecimal(highlightCandidate.getPriorityScore(), 6.0) < 0 || compareWithBigDecimal(highlightCandidate.getPriorityScore(), 8.0) >= 0) {
                                    log.info("typeB find start time. not meet score. skip. videoId: {}, highlight type: {}, cur score: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                            videoDto.getId(), HighlightEnum.Textover, highlightCandidate.getPriorityScore(), highlightCandidate, cur_workflowInstanceId_index, targetTypeStrategy);
                                    continue;
                                }

                                // 校验是否有同时间的Acoustic类型高光
                                if (!findAcoustic(highlightCandidate, highlightCandidates, cur_workflowInstanceId_index)) {
                                    log.info("typeB find start, findAcoustic by textOver, no find meet acoustic highlight, cur textOver highlight: {}, videoId: {}, cur_workflowInstanceId_index: {}",
                                            JsonUtils.toJson(highlightCandidate), videoDto.getId(), cur_workflowInstanceId_index);
                                    continue;
                                }

                                // TextOver类高光
                                // 该片段StartTime，直接作为剪辑视频开场时间点即可 作为剪辑视频开场时间点
                                VideoDto tempVideoDto = new VideoDto();
                                BeanUtils.copyProperties(videoDto, tempVideoDto);
                                String startTime = highlightCandidate.startTime;

                                // 如果尾部有定格时间
                                if (StringUtils.isNotBlank(tempVideoDto.getEndTime())) {
                                    // 如果尾部定格时间在当前高光时间之前, 则跳过
                                    if (compareWithBigDecimal(Double.parseDouble(tempVideoDto.getEndTime()), Double.parseDouble(startTime)) <= 0) {
                                        // 无对应音频, 跳过
                                        log.info("typeB find start. effect endTime before cur find startTime. skip for videoId: {}, highlight type: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                                videoDto.getId(), HighlightEnum.Textover, JsonUtils.toJson(highlightCandidate), cur_workflowInstanceId_index, targetTypeStrategy);
                                        continue;
                                    }
                                }

                                tempVideoDto.setStartTime(startTime);
                                // 去重高光
                                String unique = videoDto.getId() + "_" + tempVideoDto.getStartTime();

                                if (highlightUniqueSet.contains(unique)) {
                                    log.info("typeB find start. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Textover, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                    continue;
                                }

                                perStrategyList.add(tempVideoDto);
                                // 找到跳出
                                findStart = true;
                                highlightStartTime = currentTotalDuration.add(new BigDecimal(tempVideoDto.getStartTime())).toString();
                                addUniqueKey(highlightUniqueSet, unique);
                                log.info("typeB find drama startTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                        HighlightEnum.Textover, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                break;
                            }

                            // 3. Acoustic Clip Score识别出的8.5分 为A类
                            if (highlightCandidate.source == HighlightEnum.Acoustic) {
                                // 得分不满足, 跳过
                                if (compareWithBigDecimal(highlightCandidate.getPriorityScore(), 7.0) < 0 || compareWithBigDecimal(highlightCandidate.getPriorityScore(), 8.5) >= 0) {
                                    log.info("typeB find startTime. not meet score. skip. videoId: {}, highlight type: {}, cur score: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                            videoDto.getId(), HighlightEnum.Acoustic, highlightCandidate.getPriorityScore(), highlightCandidate, cur_workflowInstanceId_index, targetTypeStrategy);
                                    continue;
                                }

                                // 校验是否有同时间的TextOver类型高光
                                if (!findTextOver(highlightCandidate, highlightCandidates, cur_workflowInstanceId_index)) {
                                    log.info("typeB find start, findTextOver by Acoustic, no find meet textOver highlight, cur acoustic highlight: {}, videoId: {}, cur_workflowInstanceId_index: {}",
                                            JsonUtils.toJson(highlightCandidate), videoDto.getId(), cur_workflowInstanceId_index);
                                    continue;
                                }

                                // TextOver类高光
                                // 该片段StartTime，直接作为剪辑视频开场时间点即可 作为剪辑视频开场时间点
                                VideoDto tempVideoDto = new VideoDto();
                                BeanUtils.copyProperties(videoDto, tempVideoDto);
                                String startTime = highlightCandidate.startTime;

                                // 如果尾部有定格时间
                                if (StringUtils.isNotBlank(tempVideoDto.getEndTime())) {
                                    // 如果尾部定格时间在当前高光时间之前, 则跳过
                                    if (compareWithBigDecimal(Double.parseDouble(tempVideoDto.getEndTime()), Double.parseDouble(startTime)) <= 0) {
                                        // 无对应音频, 跳过
                                        log.info("typeB find start. effect endTime before cur find startTime. skip for videoId: {}, highlight type: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                                videoDto.getId(), HighlightEnum.Acoustic, JsonUtils.toJson(highlightCandidate), cur_workflowInstanceId_index, targetTypeStrategy);
                                        continue;
                                    }
                                }

                                tempVideoDto.setStartTime(startTime);
                                // 去重高光
                                String unique = videoDto.getId() + "_" + tempVideoDto.getStartTime();

                                if (highlightUniqueSet.contains(unique)) {
                                    log.info("typeB find start. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Acoustic, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                    continue;
                                }

                                perStrategyList.add(tempVideoDto);
                                // 找到跳出
                                findStart = true;
                                highlightStartTime = currentTotalDuration.add(new BigDecimal(tempVideoDto.getStartTime())).toString();
                                addUniqueKey(highlightUniqueSet, unique);
                                log.info("typeB find drama endTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                        HighlightEnum.Acoustic, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                break;
                            }
                        }

                        // 如果找到高光视频, 需要检验尾部找视频的时间范围是否还在这个视频里
                        if (findStart) {
                            double rangeStart = targetTypeStrategy.getVideoDurationPatternType().getRangeStart() * 60;
                            double rangeEnd = targetTypeStrategy.getVideoDurationPatternType().getRangeEnd() * 60;
                            endHighlightSelectRangeStartTime = new BigDecimal(highlightStartTime).add(new BigDecimal(String.valueOf(rangeStart))).toString();
                            endHighlightSelectRangeEndTime = new BigDecimal(highlightStartTime).add(new BigDecimal(String.valueOf(rangeEnd))).toString();

                            // 在当前视频总找满足条件的高光
                            for (HighlightCandidate highlightCandidate : highlightCandidates) {
                                // 1. 视觉都是高光A类, B类跳过
                                if (highlightCandidate.source == HighlightEnum.Vision) {
                                    continue;
                                }

                                // 计算高光时刻的时间点
                                double highlightTime = currentTotalDuration.add(new BigDecimal(String.valueOf(highlightCandidate.getStartTime()))).doubleValue();
                                // 检验高光是否在允许范围内
                                if (compareWithBigDecimal(highlightTime, new BigDecimal(endHighlightSelectRangeStartTime).doubleValue()) < 0 || compareWithBigDecimal(highlightTime, new BigDecimal(endHighlightSelectRangeEndTime).doubleValue()) > 0) {
                                    log.info("typeB find end. out of endTime range skip. videoId: {}, current highlight startTime: {}, compute highlight time: {}, startHighlightSelectRangeEndTime: {}, cur_workflowInstanceId_index: {}",
                                            videoDto.getId(), highlightCandidate.getStartTime(), highlightTime, startHighlightSelectRangeEndTime, cur_workflowInstanceId_index);
                                    // 超过允许范围, 找下一个
                                    continue;
                                }

                                // 2. TextOver Clip Score识别出的8分以上的 为A类
                                if (highlightCandidate.source == HighlightEnum.Textover) {
                                    // 得分不满足, 跳过
                                    if (compareWithBigDecimal(highlightCandidate.getPriorityScore(), 6.0) < 0 || compareWithBigDecimal(highlightCandidate.getPriorityScore(), 8.0) >= 0) {
                                        log.info("typeB find end time. not meet score. skip. videoId: {}, highlight type: {}, cur score: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                                videoDto.getId(), HighlightEnum.Textover, highlightCandidate.getPriorityScore(), highlightCandidate, cur_workflowInstanceId_index, targetTypeStrategy);
                                        continue;
                                    }

                                    // 校验是否有同时间的Acoustic类型高光
                                    if (!findAcoustic(highlightCandidate, highlightCandidates, cur_workflowInstanceId_index)) {
                                        log.info("typeB find end. findAcoustic by textOver, no find meet acoustic highlight, find next. cur textOver highlight: {}, videoId: {}, cur_workflowInstanceId_index: {}",
                                                JsonUtils.toJson(highlightCandidate), videoDto.getId(), cur_workflowInstanceId_index);
                                        continue;
                                    }

                                    // 去重高光
                                    String unique = videoDto.getId() + "_" + highlightCandidate.endTime;

                                    if (highlightUniqueSet.contains(unique)) {
                                        log.info("typeB find end. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Textover, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                        continue;
                                    }

                                    // TextOver类高光
                                    //  - 该片段EndTime，直接作为剪辑视频结尾点时间点即可
                                    VideoDto tempVideoDto = perStrategyList.get(perStrategyList.size() - 1);
                                    tempVideoDto.setEndTime(highlightCandidate.endTime);
                                    // 找到跳出
                                    complete = true;
                                    addUniqueKey(highlightUniqueSet, unique);
                                    log.info("typeB find drama endTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                            HighlightEnum.Textover, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                    break;
                                }

                                // 3. Acoustic Clip Score识别出的8.5分 为A类
                                if (highlightCandidate.source == HighlightEnum.Acoustic) {
                                    // 得分不满足, 跳过
                                    if (compareWithBigDecimal(highlightCandidate.getPriorityScore(), 7.0) < 0 || compareWithBigDecimal(highlightCandidate.getPriorityScore(), 8.5) >= 0) {
                                        log.info("typeB find endTime. not meet score. skip. videoId: {}, highlight type: {}, cur score: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                                videoDto.getId(), HighlightEnum.Acoustic, highlightCandidate.getPriorityScore(), highlightCandidate, cur_workflowInstanceId_index, targetTypeStrategy);
                                        continue;
                                    }

                                    // 校验是否有同时间的TextOver类型高光
                                    if (!findTextOver(highlightCandidate, highlightCandidates, cur_workflowInstanceId_index)) {
                                        log.info("typeB find end, findTextOver by Acoustic, no find meet textOver highlight, find next. cur acoustic highlight: {}, videoId: {}, cur_workflowInstanceId_index: {}",
                                                JsonUtils.toJson(highlightCandidate), videoDto.getId(), cur_workflowInstanceId_index);
                                        continue;
                                    }

                                    // 去重高光
                                    String unique = videoDto.getId() + "_" + highlightCandidate.endTime;

                                    if (highlightUniqueSet.contains(unique)) {
                                        log.info("typeB find end. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Acoustic, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                        continue;
                                    }

                                    // TextOver类高光
                                    //  - 该片段EndTime，直接作为剪辑视频结尾时间点即可
                                    VideoDto tempVideoDto = perStrategyList.get(perStrategyList.size() - 1);
                                    tempVideoDto.setEndTime(highlightCandidate.endTime);
                                    // 找到跳出
                                    complete = true;
                                    addUniqueKey(highlightUniqueSet, unique);
                                    log.info("typeA typeB drama endTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                            HighlightEnum.Acoustic, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                    break;
                                }
                            }
                        }
                    } else {
                        // 如果上一个视频已经超过了可查询结尾高光的范围
                        if (compareWithBigDecimal(currentTotalDuration.doubleValue(), new BigDecimal(endHighlightSelectRangeEndTime).doubleValue()) >= 0) {
                            // 超过允许范围, 不记录中间连续list, 跳出报错
                            log.error("typeB find end. greater than endHighlightSelectRangeEndTime, but also no find end highlight. strategy: {}, uid: {}, currentTotalDuration: {}, endHighlightSelectRangeEndTime: {}, workflowInstanceId_index: {}",
                                    targetTypeStrategy, uid, currentTotalDuration, endHighlightSelectRangeEndTime, cur_workflowInstanceId_index);
                            break;
                        }

                        // 检验高光是否在允许范围内
                        double curTotalDuration = currentTotalDuration.add(new BigDecimal(String.valueOf(videoDto.getDuration()))).doubleValue();

                        if (compareWithBigDecimal(curTotalDuration, new BigDecimal(endHighlightSelectRangeStartTime).doubleValue()) < 0) {
                            log.info("typeB find end. curTotalDuration less than endHighlightSelectRangeStartTime. record middle video, find next video. videoId: {}, current video curTotalDuration: {}, endHighlightSelectRangeStartTime: {}, cur_workflowInstanceId_index: {}",
                                    videoDto.getId(), curTotalDuration, endHighlightSelectRangeStartTime, cur_workflowInstanceId_index);
                            // 超过允许范围, 记录中间连续list, 然后找下一个
                            VideoDto tempVideoDto = new VideoDto();
                            BeanUtils.copyProperties(videoDto, tempVideoDto);
                            perStrategyList.add(tempVideoDto);
                            // 统计连续视频累计时长, 后置计算
                            currentTotalDuration = currentTotalDuration.add(new BigDecimal(String.valueOf(videoDto.getDuration())));
                            continue;
                        }

                        List<HighlightCandidate> highlightCandidates = highlightMap.get(videoDto.getId());

                        if (highlightCandidates == null || highlightCandidates.isEmpty()) {
                            log.info("typeB find end skip. find next, highlightCandidates is empty, videoId: {}, cur_workflowInstanceId_index: {}", videoDto.getId(), cur_workflowInstanceId_index);
                            // 当前无高光, 记录中间连续list, 然后找下一个
                            VideoDto tempVideoDto = new VideoDto();
                            BeanUtils.copyProperties(videoDto, tempVideoDto);
                            perStrategyList.add(tempVideoDto);
                            // 统计连续视频累计时长, 后置计算
                            currentTotalDuration = currentTotalDuration.add(new BigDecimal(String.valueOf(videoDto.getDuration())));
                            continue;
                        }

                        log.info("typeB find end. videoId: {}, highlightCandidates size: {}", videoDto.getId(), highlightCandidates.size());

                        // 在当前视频总找满足条件的高光
                        for (HighlightCandidate highlightCandidate : highlightCandidates) {
                            // 1. 视觉都是高光A类, B类跳过
                            if (highlightCandidate.source == HighlightEnum.Vision) {
                                continue;
                            }

                            // 计算高光时刻的时间点
                            double highlightTime = currentTotalDuration.add(new BigDecimal(String.valueOf(highlightCandidate.getStartTime()))).doubleValue();
                            // 检验高光是否在允许范围内
                            if (compareWithBigDecimal(highlightTime, new BigDecimal(endHighlightSelectRangeStartTime).doubleValue()) < 0 || compareWithBigDecimal(highlightTime, new BigDecimal(endHighlightSelectRangeEndTime).doubleValue()) > 0) {
                                log.info("typeB find end. find next. out of endTime range skip. videoId: {}, current highlight startTime: {}, compute highlight time: {}, endHighlightSelectRangeStartTime: {}, endHighlightSelectRangeEndTime: {}, cur_workflowInstanceId_index: {}",
                                        videoDto.getId(), highlightCandidate.getStartTime(), highlightTime, endHighlightSelectRangeStartTime, endHighlightSelectRangeEndTime, cur_workflowInstanceId_index);
                                // 超过允许范围, 找下一个
                                continue;
                            }

                            // 2. Textover Clip Score识别出的8分以上的 为A类
                            if (highlightCandidate.source == HighlightEnum.Textover) {
                                // 得分不满足, 跳过
                                if (compareWithBigDecimal(highlightCandidate.getPriorityScore(), 6.0) < 0 || compareWithBigDecimal(highlightCandidate.getPriorityScore(), 8.0) >= 0) {
                                    log.info("typeB find end time. not meet score. skip. videoId: {}, highlight type: {}, cur score: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                            videoDto.getId(), HighlightEnum.Textover, highlightCandidate.getPriorityScore(), highlightCandidate, cur_workflowInstanceId_index, targetTypeStrategy);
                                    continue;
                                }

                                // 校验是否有同时间的Acoustic类型高光
                                if (!findAcoustic(highlightCandidate, highlightCandidates, cur_workflowInstanceId_index)) {
                                    log.info("typeB find end. findAcoustic by textOver, no find meet acoustic highlight, find next. cur textOver highlight: {}, videoId: {}, cur_workflowInstanceId_index: {}",
                                            JsonUtils.toJson(highlightCandidate), videoDto.getId(), cur_workflowInstanceId_index);
                                    continue;
                                }

                                // 去重高光
                                String unique = videoDto.getId() + "_" + highlightCandidate.endTime;

                                if (highlightUniqueSet.contains(unique)) {
                                    log.info("typeB find end. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Textover, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                    continue;
                                }

                                // TextOver类高光
                                //  - 该片段EndTime，直接作为剪辑视频结尾点时间点即可
                                VideoDto tempVideoDto = new VideoDto();
                                BeanUtils.copyProperties(videoDto, tempVideoDto);
                                tempVideoDto.setEndTime(highlightCandidate.endTime);
                                perStrategyList.add(tempVideoDto);
                                // 找到跳出
                                complete = true;
                                addUniqueKey(highlightUniqueSet, unique);
                                log.info("typeB find drama startTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                        HighlightEnum.Textover, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                break;
                            }

                            // 3. Acoustic Clip Score识别出的8.5分 为A类
                            if (highlightCandidate.source == HighlightEnum.Acoustic) {
                                // 得分不满足, 跳过
                                if (compareWithBigDecimal(highlightCandidate.getPriorityScore(), 7.0) < 0 || compareWithBigDecimal(highlightCandidate.getPriorityScore(), 8.5) >= 0) {
                                    log.info("typeB find endTime. not meet score. skip. videoId: {}, highlight type: {}, cur score: {}, cur highlight: {}, workflowInstanceId_index: {}, targetTypeStrategy: {}",
                                            videoDto.getId(), HighlightEnum.Acoustic, highlightCandidate.getPriorityScore(), highlightCandidate, cur_workflowInstanceId_index, targetTypeStrategy);
                                    continue;
                                }

                                // 校验是否有同时间的TextOver类型高光
                                if (!findTextOver(highlightCandidate, highlightCandidates, cur_workflowInstanceId_index)) {
                                    log.info("typeB find end, findTextOver by Acoustic, no find meet textOver highlight, find next. cur acoustic highlight: {}, videoId: {}, cur_workflowInstanceId_index: {}",
                                            JsonUtils.toJson(highlightCandidate), videoDto.getId(), cur_workflowInstanceId_index);
                                    continue;
                                }

                                // 去重高光
                                String unique = videoDto.getId() + "_" + highlightCandidate.endTime;

                                if (highlightUniqueSet.contains(unique)) {
                                    log.info("typeB find end. duplicate skip. type: {}, videoId: {}, unique: {}, cur_workflowInstanceId_index: {}", HighlightEnum.Acoustic, videoDto.getId(), unique, cur_workflowInstanceId_index);
                                    continue;
                                }

                                // TextOver类高光
                                //  - 该片段EndTime，直接作为剪辑视频结尾时间点即可
                                VideoDto tempVideoDto = new VideoDto();
                                BeanUtils.copyProperties(videoDto, tempVideoDto);
                                tempVideoDto.setEndTime(highlightCandidate.endTime);
                                perStrategyList.add(tempVideoDto);
                                // 找到跳出
                                complete = true;
                                addUniqueKey(highlightUniqueSet, unique);
                                log.info("typeB find drama endTime. type: {}, videoId: {}, uid: {}, highlightCandidate: {}, startTime: {}, highlightInfo: {}, currentTotalDuration: {}, workflowInstanceId_index: {}",
                                        HighlightEnum.Acoustic, tempVideoDto.getId(), tempVideoDto.getUid(), highlightCandidate.getId(), tempVideoDto.getStartTime(), new Gson().toJson(highlightCandidate), currentTotalDuration, cur_workflowInstanceId_index);
                                break;
                            }
                        }

                        // 在当前连续视频中未找到符合条件的高光, 记录连续视频
                        if (!complete) {
                            log.info("typeB find end. current video no complete find highlight. find next, videoId: {}, cur_workflowInstanceId_index: {}",
                                    videoDto.getId(), cur_workflowInstanceId_index);
                            // 当前无高光, 记录中间连续list, 然后找下一个
                            VideoDto tempVideoDto = new VideoDto();
                            BeanUtils.copyProperties(videoDto, tempVideoDto);
                            perStrategyList.add(tempVideoDto);
                            // 统计连续视频累计时长, 后置计算
                            currentTotalDuration = currentTotalDuration.add(new BigDecimal(String.valueOf(videoDto.getDuration())));
                            continue;
                        }
                    }

                    // 统计连续视频累计时长, 后置计算
                    currentTotalDuration = currentTotalDuration.add(new BigDecimal(String.valueOf(videoDto.getDuration())));
                }

            }

            index++;
            log.info("<<<<< end exe cur_workflowInstanceId_index: {}", cur_workflowInstanceId_index);
            result.add(perStrategyList);
        }

        result = result.stream().filter(CollectionUtils::isNotEmpty).collect(Collectors.toList());
        log.info("found {} for targetTypeStrategy, strategy: {}. result:{}, uid: {}, workflowInstanceId: {}", result.size(), targetTypeStrategeList, JsonUtils.toJson(result), uid, workflowInstanceId);
        return result;
    }

    /**
     * 随机选取当前策略的开头视频位置
     *
     * @param videos                           当前需要元视频列表
     * @param startHighlightSelectRangeEndTime 计算出来开始高光寻找的截止时间
     * @param curCacheUsed                     选择随机视频使用，已经用过的视频会缓存到这里
     * @return 当前策略随机选取的开头视频id
     */
    private Long selectRandomVideoId(List<VideoDto> videos, String startHighlightSelectRangeEndTime, Set<Long> curCacheUsed) {
        // 计算随机选取开头视频的数据集（因为需要倒推剪掉一部分时间, 参照startHighlightSelectRangeEndTime）
        int computeCount = 0;
        BigDecimal currentTotalDuration = BigDecimal.ZERO;
        BigDecimal requiredEnd = new BigDecimal(startHighlightSelectRangeEndTime);

        for (int i = 0; i < videos.size(); i++) {
            computeCount++;
            VideoDto videoDto = videos.get(i);
            currentTotalDuration = currentTotalDuration.add(new BigDecimal(String.valueOf(videoDto.getDuration())));

            if (currentTotalDuration.compareTo(requiredEnd) >= 0) {
                computeCount = i;
                break;
            }
        }

        // 随机选择, 上限100, 防止死循环
        for (int i = 0; i < 100; i++) {
            Random random = new Random();
            // random.nextInt(6)生成 0（包含）到 6（不包含）之间的整数
            int randomNum = random.nextInt(computeCount + 1);
            Long id = videos.get(randomNum).getId();

            // 包含重新计算 or 超过限制, 则就使用当前(比如: set: [1,2,3], random范围: 1-3, 第四次生成已经覆盖到set了, 则随机取一个, 不再循环)
            if (curCacheUsed.contains(id) && curCacheUsed.size()<= computeCount) {
                continue;
            }

            curCacheUsed.add(id);
            return id;
        }

        // 理论上不会执行到这里
        return videos.get(0).getId();
    }

    private void addUniqueKey(Set<String> set, String key) {
        String time = key.split("_")[1];
        // 不记录结束时间是null的情况
        if (time.equals("null")) {
            return;
        }

        set.add(key);
    }

    /**
     * 从数据库查询这些视频中已经用过的高光片段
     */
    private Set<String> findDuplicateInDb(String uid, List<VideoDto> videos) {
        Set<String> set = new HashSet<>();
        List<Long> videoIds = videos.stream().map(VideoDto::getId).toList();
        List<ClipRecord> startHighlightInfo = clipRecordService.findByUidAndStartVideoIdIn(uid, videoIds);

        if (CollectionUtils.isNotEmpty(startHighlightInfo)) {
            set.addAll(startHighlightInfo.stream().map(clipRecord -> clipRecord.getStartVideoId() + "_" + clipRecord.getStartTime()).collect(Collectors.toSet()));
            log.info("findDuplicateInDb startHighlightInfo: {}", startHighlightInfo);
        }

        List<ClipRecord> endHighlightInfo = clipRecordService.findByUidAndEndVideoIdIn(uid, videoIds);

        if (CollectionUtils.isNotEmpty(endHighlightInfo)) {
            // 不记录endTime为null的情况
            set.addAll(endHighlightInfo.stream().filter(clipRecord -> StringUtils.isNotBlank(clipRecord.getEndTime())).map(clipRecord -> clipRecord.getEndVideoId() + "_" + clipRecord.getEndTime()).collect(Collectors.toSet()));
        }

        return set;
    }

    /**
     * 找当前高光视频片段对应的音效片段的StartTime 作为剪辑视频开场时间点
     *
     * @param cur                 当前视觉的高光
     * @param highlightCandidates 当前视频的所有高光
     * @return 对应音频片段的startTime
     */
    private String findStartAcousticByVision(HighlightCandidate cur, List<HighlightCandidate> highlightCandidates, String cur_workflowInstanceId_index) {
        for (HighlightCandidate candidate : highlightCandidates) {
            if (candidate.source == HighlightEnum.Acoustic) {
                if (compareWithBigDecimal(cur.getPriorityScore(), Double.parseDouble(candidate.getStartTime())) >= 0
                        && compareWithBigDecimal(cur.getPriorityScore(), Double.parseDouble(candidate.getEndTime())) <= 0) {
                    log.info("findStartAcousticByVision current vision highlight: {}, find acoustic highlight: {}, cur_workflowInstanceId_index: {}",
                            JsonUtils.toJson(cur), JsonUtils.toJson(candidate), cur_workflowInstanceId_index);
                    return candidate.getStartTime();
                }
            }
        }

        return null;
    }

    /**
     * 找当前高光视频片段对应的音效或者台词片段的endTime 作为剪辑视频结束的高光开场时间点
     * <p>
     * - Vision类高光
     * - 【判断】Vision HighlightTime的时间点  是否 落在某个TextOver类或Acoustic类的高光片段中
     * - 均否，则用HighlightTime的StartTime作为开场时间点
     * - TextOver是，或，Acoustic是，则用  是的那个  的EndTime作为开场时间点（比如TextOver是，则用TextOver的EndTime作为开场时间点）
     * - 均是，则用TextOver的EndTime为开场时间点
     *
     * @param cur                 当前视觉的高光
     * @param highlightCandidates 当前视频的所有高光
     * @return 对应的音效或者台词片段的endTime
     */
    private String findEndAcousticOrTextOverByVision(HighlightCandidate cur, List<HighlightCandidate> highlightCandidates, String cur_workflowInstanceId_index) {
        for (HighlightCandidate candidate : highlightCandidates) {
            // 先找textOver的
            if (candidate.source == HighlightEnum.Textover) {
                if (compareWithBigDecimal(cur.getPriorityScore(), Double.parseDouble(candidate.getStartTime())) >= 0
                        && compareWithBigDecimal(cur.getPriorityScore(), Double.parseDouble(candidate.getEndTime())) <= 0) {
                    log.info("findEndAcousticOrTextOverByVision find textOver type, current vision highlight: {}, textOver highlight: {}, cur_workflowInstanceId_index: {}",
                            JsonUtils.toJson(cur), JsonUtils.toJson(candidate), cur_workflowInstanceId_index);
                    return candidate.getEndTime();
                }
            }
        }

        for (HighlightCandidate candidate : highlightCandidates) {
            if (candidate.source == HighlightEnum.Acoustic) {
                if (compareWithBigDecimal(cur.getPriorityScore(), Double.parseDouble(candidate.getStartTime())) >= 0
                        && compareWithBigDecimal(cur.getPriorityScore(), Double.parseDouble(candidate.getEndTime())) <= 0) {
                    log.info("findEndAcousticOrTextOverByVision find acoustic type, current vision highlight: {}, acoustic highlight: {}, cur_workflowInstanceId_index: {}",
                            JsonUtils.toJson(cur), JsonUtils.toJson(candidate), cur_workflowInstanceId_index);
                    return candidate.getEndTime();
                }
            }
        }

        return null;
    }

    private boolean findTextOver(HighlightCandidate cur, List<HighlightCandidate> highlightCandidates, String cur_workflowInstanceId_index) {
        for (HighlightCandidate candidate : highlightCandidates) {
            if (candidate.source == HighlightEnum.Textover) {
                if (compareWithBigDecimal(Double.parseDouble(cur.getStartTime()), Double.parseDouble(candidate.getStartTime())) >= 0
                        && compareWithBigDecimal(Double.parseDouble(cur.getStartTime()), Double.parseDouble(candidate.getEndTime())) <= 0
                        && checkTextOverInBType(candidate)) {
                    log.info("typeB findTextOver by acoustic, acoustic startTime between textOver startTime and endTime, cur acoustic highlight: {}, find textOver highlight: {}, cur_workflowInstanceId_index: {}",
                            JsonUtils.toJson(cur), JsonUtils.toJson(candidate), cur_workflowInstanceId_index);
                    return true;
                }

                if (compareWithBigDecimal(Double.parseDouble(cur.getEndTime()), Double.parseDouble(candidate.getStartTime())) >= 0
                        && compareWithBigDecimal(Double.parseDouble(cur.getEndTime()), Double.parseDouble(candidate.getEndTime())) <= 0
                        && checkTextOverInBType(candidate)) {
                    log.info("typeB findTextOver by acoustic, acoustic endTime between textOver startTime and endTime, cur acoustic highlight: {}, find textOver highlight: {}, cur_workflowInstanceId_index: {}",
                            JsonUtils.toJson(cur), JsonUtils.toJson(candidate), cur_workflowInstanceId_index);
                    return true;
                }

                if (compareWithBigDecimal(Double.parseDouble(cur.getStartTime()), Double.parseDouble(candidate.getStartTime())) <= 0
                        && compareWithBigDecimal(Double.parseDouble(cur.getEndTime()), Double.parseDouble(candidate.getEndTime())) >= 0
                        && checkTextOverInBType(candidate)) {
                    log.info("typeB findTextOver by acoustic, acoustic startTime and endTime all between textOver startTime and endTime, cur acoustic highlight: {}, find textOver highlight: {}, cur_workflowInstanceId_index: {}",
                            JsonUtils.toJson(cur), JsonUtils.toJson(candidate), cur_workflowInstanceId_index);
                    return true;
                }
            }
        }

        return false;
    }

    private boolean checkAcousticInBType(HighlightCandidate candidate) {
        return compareWithBigDecimal(candidate.getPriorityScore(), 7.0) >= 0 && compareWithBigDecimal(candidate.getPriorityScore(), 8.5) < 0;
    }

    private boolean findAcoustic(HighlightCandidate cur, List<HighlightCandidate> highlightCandidates, String cur_workflowInstanceId_index) {
        for (HighlightCandidate candidate : highlightCandidates) {
            if (candidate.source == HighlightEnum.Acoustic) {
                if (compareWithBigDecimal(Double.parseDouble(cur.getStartTime()), Double.parseDouble(candidate.getStartTime())) >= 0
                        && compareWithBigDecimal(Double.parseDouble(cur.getStartTime()), Double.parseDouble(candidate.getEndTime())) <= 0
                        && checkAcousticInBType(candidate)) {
                    log.info("typeB findAcoustic by textOver, textOver startTime between acoustic startTime and endTime, cur textOver highlight: {}, find acoustic highlight: {}, cur_workflowInstanceId_index: {}",
                            JsonUtils.toJson(cur), JsonUtils.toJson(candidate), cur_workflowInstanceId_index);
                    return true;
                }

                if (compareWithBigDecimal(Double.parseDouble(cur.getEndTime()), Double.parseDouble(candidate.getStartTime())) >= 0
                        && compareWithBigDecimal(Double.parseDouble(cur.getEndTime()), Double.parseDouble(candidate.getEndTime())) <= 0
                        && checkAcousticInBType(candidate)) {
                    log.info("typeB findAcoustic by textOver, textOver endTime between acoustic startTime and endTime, cur textOver highlight: {}, find acoustic highlight: {}, cur_workflowInstanceId_index: {}",
                            JsonUtils.toJson(cur), JsonUtils.toJson(candidate), cur_workflowInstanceId_index);
                    return true;
                }

                if (compareWithBigDecimal(Double.parseDouble(cur.getStartTime()), Double.parseDouble(candidate.getStartTime())) <= 0
                        && compareWithBigDecimal(Double.parseDouble(cur.getEndTime()), Double.parseDouble(candidate.getEndTime())) >= 0
                        && checkAcousticInBType(candidate)) {
                    log.info("typeB findAcoustic by textOver, textOver startTime and endTime all between acoustic startTime and endTime, cur textOver highlight: {}, find acoustic highlight: {}, cur_workflowInstanceId_index: {}",
                            JsonUtils.toJson(cur), JsonUtils.toJson(candidate), cur_workflowInstanceId_index);
                    return true;
                }
            }
        }

        return false;
    }

    private boolean checkTextOverInBType(HighlightCandidate candidate) {
        return compareWithBigDecimal(candidate.getPriorityScore(), 6.0) >= 0 && compareWithBigDecimal(candidate.getPriorityScore(), 8.0) < 0;
    }

    private Map<Long, List<HighlightCandidate>> getHighlightMap(String uid, String workflowInstanceId, List<VideoDto> videos) {
        Map<Long, List<HighlightCandidate>> highlightMap = new HashMap<>();

        for (VideoDto video : videos) {
            Long videoId = video.getId();
            List<Highlight> highlightList = highlightRepository.findByUidAndVideoId(uid, video.getId());

            if (highlightList == null) {
                log.info("highlight list is empty. workflowInstanceId: {}, videoId: {}", workflowInstanceId, videoId);
                continue;
            }

            // 当前视频的高光集合
            List<HighlightCandidate> highlightCandidates = new ArrayList<>();

            for (Highlight highlight : highlightList) {
                int type = highlight.getType();
                HighlightEnum highlightEnum = HighlightEnum.fromCode(type);

                if (highlightEnum == null) {
                    log.error("no such highlight type: {}, uid: {}, workflowInstanceId: {}, videoId: {}", type, uid, workflowInstanceId, videoId);
                    continue;
                }

                switch (highlightEnum) {
                    case Textover:
                        try {
                            String textData = highlight.getData();
                            JsonNode jsonNodeArray = JsonUtils.getInstance().readTree(textData);

                            if (jsonNodeArray.isArray()) {
                                for (JsonNode jsonNode : jsonNodeArray) {
                                    String startTime = convertToSeconds(jsonNode.get("startTime").asText());
                                    String endTime = convertToSeconds(jsonNode.get("endTime").asText());
                                    int severity = jsonNode.get("severity").asInt();
//                                    String scenario = jsonNode.get("scenario").asText();
                                    highlightCandidates.add(new HighlightCandidate(highlight.getId(), video.getId(), startTime, endTime, HighlightEnum.Textover, severity));
                                }
                            }
                        } catch (Exception e) {
                            log.error("parse highlight data error. workflowInstanceId: {}. highlightId: {}, data: {}", workflowInstanceId, highlight.getId(), highlight.getData(), e);
                        }
                        break;
                    case Vision:
                        try {
                            String visionData = highlight.getData();
                            JsonNode analysisResultsArray = JsonUtils.getInstance().readValue(visionData, JsonNode.class).get("analysis_results");
                            if (analysisResultsArray.isArray()) {
                                for (JsonNode jsonNode : analysisResultsArray) {
                                    JsonNode analysisNode = jsonNode.get("analysis");
                                    analysisNode.fields().forEachRemaining(entry -> {
                                        JsonNode value = entry.getValue();
                                        JsonNode highlightsArray = value.get("highlights");

                                        if (highlightsArray != null && highlightsArray.isArray()) {
                                            for (JsonNode elemNode : highlightsArray) {
                                                AtomicReference<String> startTime = new AtomicReference<>();
                                                AtomicReference<String> endTime = new AtomicReference<>();
                                                AtomicReference<Double> timePoint = new AtomicReference<>();

                                                elemNode.fields().forEachRemaining(entry2 -> {
                                                    if (entry2.getKey().contains("startTime")) {
                                                        startTime.set(entry2.getValue().asText());
                                                    }

                                                    if (entry2.getKey().contains("endTime")) {
                                                        endTime.set(entry2.getValue().asText());
                                                    }

                                                    if (entry2.getKey().contains("timepoint")) {
                                                        timePoint.set(entry2.getValue().asDouble());
                                                    }
                                                });

                                                if (startTime.get() == null || endTime.get() == null || timePoint.get() == null) {
                                                    log.error("parse highlight vision data error. startTime: {}, endTime: {}, timepoint: {}, workflowInstanceId: {}. highlightId: {}, data: {} ", startTime, endTime, timePoint, workflowInstanceId, highlight.getId(), highlight.getData());
                                                    break;
                                                }

                                                highlightCandidates.add(new HighlightCandidate(highlight.getId(), video.getId(), startTime.get(), endTime.get(), HighlightEnum.Vision, timePoint.get()));
                                            }
                                        } else {
                                            log.error("parse highlight vision data error. highlights no array. workflowInstanceId: {}. highlightId: {}, data: {}", workflowInstanceId, highlight.getId(), highlight.getData());
                                        }
                                    });
                                }
                            }
                        } catch (Exception e) {
                            log.error("parse highlight data error. workflowInstanceId: {}. highlightId: {}, data: {}", workflowInstanceId, highlight.getId(), highlight.getData(), e);
                        }
                        break;
                    case Acoustic:
                        try {
                            String visionData = highlight.getData();

                            // 去除最外层双引号
                            if (visionData.startsWith("\"") && visionData.endsWith("\"")) {
                                visionData = visionData.substring(1, visionData.length() - 1);
                            }

                            // 处理转义字符
                            visionData = visionData.replace("\\\"", "\"");

                            JsonNode jsonNodeArray = JsonUtils.getInstance().readTree(visionData);
                            if (jsonNodeArray.isArray()) {
                                for (JsonNode jsonNode : jsonNodeArray) {
                                    String startTime = jsonNode.get("startTime").asText();
                                    String endTime = jsonNode.get("endTime").asText();
                                    double score = jsonNode.get("score").asDouble();
                                    highlightCandidates.add(new HighlightCandidate(highlight.getId(), video.getId(), startTime, endTime, HighlightEnum.Acoustic, score));
                                }
                            }
                        } catch (Exception e) {
                            log.error("parse highlight data error. workflowInstanceId: {}. highlightId: {}, data: {}", workflowInstanceId, highlight.getId(), highlight.getData(), e);
                        }
                        break;
                    default:
                        log.error("parse highlight data error. Unexpected type, workflowInstanceId: {}. highlightId: {}, data: {}", workflowInstanceId, highlight.getId(), highlight.getData());
                }
            }


            highlightMap.put(videoId, highlightCandidates);
        }

        return highlightMap;
    }

    /**
     * 处理连续多个视频的片头片尾时间切点信息
     *
     * @param results 将要发给workFlow的参数
     */
    private void setEffectCutTime(String uid, List<List<VideoDto>> results, String workflowInstanceId) {
        // 查询所有视频特效
        List<Long> ids = results.stream().flatMap(List::stream).map(VideoDto::getId).distinct().collect(Collectors.toList());
        List<Effects> effectsList = effectsRepository.findByUidAndVideoIdIn(uid, ids);

        if (CollectionUtils.isEmpty(effectsList)) {
            log.error("No effects found for uid: {}, videoIds: {}", uid, ids);
            workflowService.updateWorkflowInstanceStatusWithError(workflowInstanceId, WorkflowInstanceStatus.FAIL, "未查询到定格特效数据");
            throw new RuntimeException("No effects found for uid: " + uid + ", videoIds: " + ids);
        }

        Map<Long, Effects> effectsMap = effectsList.stream().collect(Collectors.toMap(Effects::getVideoId, Function.identity()));

        // 循环处理3个切片特效
        for (List<VideoDto> result : results) {
            if (CollectionUtils.isEmpty(result)) {
                log.error("Processing workflow effects param error. result is empty, instance: {}, result: {}", workflowInstanceId, results);
                workflowService.updateWorkflowInstanceStatusWithError(workflowInstanceId, WorkflowInstanceStatus.FAIL, "定格特效数据异常, 某一段Result is empty");
                throw new IllegalStateException("Result is empty");
            }

            // 处理2个及以上视频, 依次处理当前视频的尾部和下一个视频的开头
            for (int i = 0; i < result.size(); i++) {
                // 只有一个视频 或者 连续视频的最后一个视频不处理, 因为前一个视频已经处理了其头部重合问题
                if (i == result.size() - 1) {
                    break;
                }

                // 处理当前的片尾, 同时处理处理下一个片头
                VideoDto videoDto = result.get(i);

                // 获取特效配置
                Effects effects = effectsMap.get(videoDto.getId());

                if (effects == null) {
                    log.error("Processing workflow effects param error. effects is null, maybe not complete effect, instance: {}, videoId: {}, result: {}", workflowInstanceId, videoDto.getId(), results);
                    workflowService.updateWorkflowInstanceStatusWithError(workflowInstanceId, WorkflowInstanceStatus.FAIL, "定格特效数据异常, 单个视频Result is empty, videoId: " + videoDto.getId());
                    throw new IllegalStateException("Result is empty");
                }

                String effectsJson = effects.getData();
                EffectConfigJson effectConfigJson = new Gson().fromJson(effectsJson, EffectConfigJson.class);

                if (effectConfigJson == null) {
                    log.error("Processing workflow effects param error. effects json format is error, instance: {}, videoId: {}, result: {}", workflowInstanceId, videoDto.getId(), results);
                    workflowService.updateWorkflowInstanceStatusWithError(workflowInstanceId, WorkflowInstanceStatus.FAIL, "定格特效数据异常, 单个视频格式异常, videoId: " + videoDto.getId());
                    throw new IllegalStateException("Result is empty");
                }

                // 先取台词重合
                EffectConfigJson.TextOverlap textOverlap = effectConfigJson.getTextOverlap();

                if (textOverlap != null && !StringUtils.isAllBlank(textOverlap.getEndOverlapTime(), textOverlap.getNextEndOverlapTime())) {
                    // 处理当前视频片尾
                    if (StringUtils.isNotBlank(textOverlap.getEndOverlapTime())) {
                        videoDto.setEndTime(textOverlap.getEndOverlapTime());
                    }
                    // 处理下一集视频开头
                    if (StringUtils.isNotBlank(textOverlap.getNextEndOverlapTime())) {
                        result.get(i + 1).setStartTime(textOverlap.getNextEndOverlapTime());
                    }

                    continue;
                }

                // 如果台词无匹配, 则取影像重合
                EffectConfigJson.ImageOverlap imageOverlap = effectConfigJson.getImageOverlap();

                if (imageOverlap != null && !StringUtils.isAllBlank(imageOverlap.getStartOverlapTime(), imageOverlap.getNextStartOverlapTime())) {
                    // 处理当前视频片尾
                    if (StringUtils.isNotBlank(imageOverlap.getStartOverlapTime())) {
                        videoDto.setEndTime(imageOverlap.getStartOverlapTime());
                    }
                    // 处理下一集视频开头
                    if (StringUtils.isNotBlank(imageOverlap.getNextStartOverlapTime())) {
                        result.get(i + 1).setStartTime(imageOverlap.getNextStartOverlapTime());
                    }

                    continue;
                }

                // 兜底方案
                if (StringUtils.isNotBlank(effectConfigJson.getStopMotionTime())) {
                    // 兜底方案只处理当前的片尾；不处理下一集开头
                    videoDto.setEndTime(effectConfigJson.getStopMotionTime());
                }
            }
        }
    }

    /**
     * 生成目标视频的策略, 比如想要生成"2-5min && A类"的视频
     */
    @Data
    @ToString
    @AllArgsConstructor
    public static class TargetTypeStrategy {

        /**
         * 暂定自动: 1; 2-5min: 2; 5-10min: 3; 10-20min: 4
         */
        private VideoDurationPatternEnum videoDurationPatternType;

        /**
         * @see ScoreGradeEnum A类、B类
         */
        private ScoreGradeEnum scoreGrade;
    }

    @Data
    @ToString
    static class EffectConfigJson {
        /**
         * 台词片头片尾重合信息
         */
        @SerializedName("text_overlap")
        private TextOverlap textOverlap;

        /**
         * 片尾特效剔除时间, 3s内和最后一个字尾部时间比对
         */
        @SerializedName("image_overlap")
        private ImageOverlap imageOverlap;

        /**
         * 片尾定格特效剔除, 3s内和最后一个字尾部时间比对
         */
        @SerializedName("stop_motion_time")
        private String stopMotionTime;


        /**
         * 音频台词模糊匹配. 台词部分取重合片段的结束时间作连接
         */
        @Data
        static class TextOverlap {

            /**
             * 片尾重合片段的尾部时间
             */
            @SerializedName("end_overlap_time")
            private String endOverlapTime;

            /**
             * 下一集片头重合片段的尾部时间
             */
            @SerializedName("next_end_overlap_time")
            private String nextEndOverlapTime;
        }

        /**
         * 图片模糊匹配, 图片部分取重合图片的开头时间作连接
         */
        @Data
        static class ImageOverlap {

            /**
             * 片尾重合片段的开始时间
             */
            @SerializedName("start_overlap_time")
            private String startOverlapTime;

            /**
             * 下一集片头重合片段的开始时间
             */
            @SerializedName("next_start_overlap_time")
            private String nextStartOverlapTime;
        }

    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class HighlightCandidate {
        Long id;
        Long videoId;
        String startTime;
        String endTime;
        HighlightEnum source; // vision / scenario / acoustic
        double priorityScore; // 用于排序，vision 用 highlightTime，scenario 用 severity，acoustic 用 score
    }

    /**
     * 将 "HH:mm:ss,SSS" 格式的时间转换为 "秒.毫秒" 格式
     *
     * @param timeString 输入时间字符串，例如 "00:01:23,456"
     * @return 转换后的字符串，例如 "83.456"
     * @throws IllegalArgumentException 如果输入格式无效
     */
    public static String convertToSeconds(String timeString) {
        // 校验输入格式
        if (timeString == null || !timeString.matches("^\\d{2}:\\d{2}:\\d{2},\\d{3}$")) {
            throw new IllegalArgumentException("Invalid time format. Expected: HH:mm:ss,SSS");
        }

        // 分割为时间部分和毫秒部分
        String[] parts = timeString.split(",", 2);
        String[] timeComponents = parts[0].split(":");

        // 提取时分秒
        int hours = Integer.parseInt(timeComponents[0]);
        int minutes = Integer.parseInt(timeComponents[1]);
        int seconds = Integer.parseInt(timeComponents[2]);
        String milliseconds = parts[1];

        // 计算总秒数
        long totalSeconds = hours * 3600L + minutes * 60L + seconds;

        // 格式化输出（确保毫秒部分保持3位）
        return String.format("%d.%s", totalSeconds, milliseconds);
    }

}
