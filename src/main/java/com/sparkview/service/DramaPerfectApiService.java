package com.sparkview.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.sparkview.common.*;
import com.sparkview.common.enums.FolderTypeEnum;
import com.sparkview.common.enums.WorkflowSourceEnum;
import com.sparkview.controller.dto.VideoDto;
import com.sparkview.persistence.*;
import com.sparkview.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @author: gavingeng
 * @create: 2025-04-21 16:31
 * @description:
 **/
@Service
@Slf4j
public class DramaPerfectApiService {

    @Value("${perfect.api.dramaUrl:http://localhost:8000/api/workflow/drama}")
    private String dramaUrl;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private WorkflowInstanceRepository workflowInstanceRepository;

    @Resource
    private VideoRepository videoRepository;

    @Resource
    private DramaStrategyService dramaStrategyService;

    @Resource
    private WorkflowService workflowService;

    @Resource
    private FolderRepository folderRepository;

    @Resource
    private SqsMessageSender sqsMessageSender;

    public void startDramaWorkflow(String workflowInstanceId) {
        WorkflowInstance workflowInstance = workflowInstanceRepository.findByWorkflowInstanceId(workflowInstanceId).get();
        startDramaWorkflow(workflowInstance);
    }

    public void startDramaWorkflow(WorkflowInstance workflowInstance) {
        String params = workflowInstance.getParams();
        ConfigTemplate configTemplate = JsonUtils.toObject(params, ConfigTemplate.class);
        startDramaWorkflow(configTemplate, workflowInstance.getWorkflowInstanceId());
    }

    public WorkflowInstance startDramaWorkflow(ConfigTemplate configTemplate, String workflowInstanceId) {
        try {
            Map<String, Object> workflowParams = getWorkflowParams(configTemplate, workflowInstanceId);
            if (workflowParams != null && !workflowParams.isEmpty()) {
                workflowInstanceId = String.valueOf(workflowParams.get("workflow_instance_id"));
                log.info("Starting drama workflow. with params {}", new Gson().toJson(workflowParams));
                CompletableFuture.runAsync(() -> {
                    try {
                        restTemplate.postForLocation(dramaUrl, workflowParams, String.class);
                    } catch (Exception e) {
                        log.error("异步请求失败: {}", e.getMessage());
                    }
                });
                log.info("Start drama workflow by Prefect API, workflowInstanceId: {}", workflowInstanceId);
            } else {
                log.error("prepare drama assets error. videoListStr is empty, videoListStr: {}", configTemplate.getValueByKey("videoList"));
            }
        } catch (Exception e) {
            log.error("Error starting drama workflow by Prefect API: {}", workflowInstanceId, e);
            workflowService.updateWorkflowInstanceStatusWithError(workflowInstanceId, WorkflowInstanceStatus.FAIL, "调用python服务异常, dramaUrl: " + dramaUrl);
            throw new RuntimeException("error");
        }
        return workflowInstanceRepository.findByWorkflowInstanceId(workflowInstanceId).get();
    }

    @Transactional
    public Map<String, Object> getWorkflowParams(ConfigTemplate configTemplate, String workflowInstanceId) {
        Map<String, Object> res = null;
        WorkflowInstance workflowInstance = null;
        try {
            String videoListStr = configTemplate.getValueByKey("videoList");
            List<VideoDto> videoDtoList = Lists.newArrayList();
            String uid = null;

            int dramaType = 0;
            String lang = null;
            String folderId = "";
            if (StringUtils.isNotEmpty(videoListStr)) {
                List<String> videos = JsonUtils.toObject(videoListStr, new TypeReference<>() {
                });

                if (CollectionUtils.isNotEmpty(videos)) {
                    for (String objectKey : videos) {
                        if (StringUtils.isNotEmpty(objectKey)) {
                            Optional<Video> videoOptional = videoRepository.findByObjectKey(objectKey);
                            if (videoOptional.isEmpty()) {
                                log.info("video not found, objectKey: {} {}", objectKey, uid);
                                return null;
                            }
                            Video video = videoOptional.get();
                            VideoDto videoDto = VideoDto.builder()
                                    .id(video.getId())
                                    .uid(video.getUid())
                                    .folder_id(video.getFolderId())
                                    .folder_name(video.getFolderName())
                                    .name(video.getName())
                                    .object_key(video.getObjectKey())
                                    .drama_type(video.getDramaType())
                                    .lang(video.getLang())
                                    .duration(video.getDuration())
                                    .status(video.getStatus()).build();

                            if (lang == null) {
                                lang = video.getLang();
                                dramaType = video.getDramaType();
                                folderId = video.getFolderId();
                                uid = video.getUid();
                            }
                            videoDtoList.add(videoDto);
                        }
                    }
                }
            }

            if (CollectionUtils.isEmpty(videoDtoList)) {
                log.error("prepare clip assets error. videoDtoList is empty, videoListStr: {}", videoListStr);
                throw new RuntimeException("prepare clip assets error. videoDtoList is empty");
            }

            Optional<Folder> folderOptional = folderRepository.findById(Long.parseLong(folderId));
            String folderName = "";
            if (folderOptional.isPresent()) {
                Folder folder = folderOptional.get();
                folderName = folder.getName();
                uid = folder.getUid();
            }

            configTemplate.setValueByKey("dramaType", String.valueOf(dramaType));
            configTemplate.setValueByKey("lang", lang);

            if (workflowInstanceId == null) {
                String outputDir = WorkflowHelper.calculateOutputDirectory(configTemplate);
                workflowInstanceId = UUID.randomUUID().toString();
                WorkflowInstanceStatus status = WorkflowInstanceStatus.PENDING;
                WorkflowInstance instance = new WorkflowInstance();
                instance.setType(FolderTypeEnum.SHORT_DRAMA);
                instance.setParams(JsonUtils.toJson(configTemplate));
                instance.setWorkflowInstanceId(workflowInstanceId);
                instance.setWorkflowName(folderName);
                instance.setUid(uid);
                instance.setStatus(status);
                instance.setRetryTimes(0);
                instance.setOutputDir(outputDir);
                instance.setSource(WorkflowSourceEnum.ORIGIN.getCode());
                instance.setPresetFilenames(JsonUtils.toJson(workflowService.getFileNames(uid, folderName, 3)));
                workflowInstance = workflowService.saveWorkflowInstance(instance);
            }


            // 对videoDtoList进行排序
            videoDtoList = VideoSortUtils.sortByObjectKeyNumber(videoDtoList);

            List<List<VideoDto>> videos;
            try {
                videos = dramaStrategyService.getClipAssetsByDefaultStrategy(uid, workflowInstanceId, configTemplate, videoDtoList);
            } catch (Exception e) {
                log.error("dramaStrategyService.getClipAssetsByDefaultStrategy error, workflowInstanceId: {}", workflowInstanceId, e);

                Map<String, String> messageBody = new HashMap<>();
                messageBody.put("workflow_instance_id", workflowInstanceId);
                messageBody.put("event", "workflow_retry");

                sqsMessageSender.sendMessage(JsonUtils.toJson(messageBody));
                return res;
            }

            String pathName = WorkflowService.getWorkflowOutputPath(workflowInstance);
            String presetFilenames = workflowInstance.getPresetFilenames();
            List<String> presetFilenamesArr = new ArrayList<>();
            if (StringUtils.isNotEmpty(presetFilenames)) {
                presetFilenamesArr = JsonUtils.toObject(presetFilenames, new TypeReference<>() {
                });
            }

            res = Maps.newHashMap();
            res.put("workflow_instance_id", workflowInstanceId);
            res.put("videos", videos);
            res.put("path_name", pathName);
            res.put("preset_filenames", presetFilenamesArr);
        } catch (Exception e) {
            log.error("getWorkflowParams error", e);
            if (workflowInstanceId != null) {
                try {
                    Map<String, String> messageBody = new HashMap<>();
                    messageBody.put("workflow_instance_id", workflowInstanceId);
                    messageBody.put("event", "workflow_retry");
                    sqsMessageSender.sendMessage(JsonUtils.toJson(messageBody));
                    log.info("已发送工作流重试消息，workflowInstanceId: {}", workflowInstanceId);
                } catch (Exception sqsEx) {
                    log.error("发送工作流重试消息失败, workflowInstanceId: {}", workflowInstanceId, sqsEx);
                }
            }
        }
        return res;
    }
}

