package com.sparkview.repository;

import com.sparkview.common.WorkflowInstanceStatus;
import com.sparkview.common.enums.FolderTypeEnum;
import com.sparkview.persistence.WorkflowInstance;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

@Repository
public interface WorkflowInstanceRepository extends JpaRepository<WorkflowInstance, Long> {

    @Query("SELECT w FROM WorkflowInstance w WHERE w.status = :status AND w.source = 0 ORDER BY w.createTime DESC")
    List<WorkflowInstance> findByStatusOrderByCreateTimeDesc(@Param("status") WorkflowInstanceStatus status, PageRequest pageRequest);

    @Query("SELECT w FROM WorkflowInstance w WHERE w.workflowInstanceId = :workflowInstanceId")
    Optional<WorkflowInstance> findByWorkflowInstanceId(@Param("workflowInstanceId") String workflowInstanceId);

    @Query("SELECT w FROM WorkflowInstance w WHERE w.uid = :uid AND w.source = 0 ORDER BY w.createTime DESC")
    List<WorkflowInstance> findByUidOrderByCreateTimeDesc(@Param("uid") String uid);

    @Query("SELECT w FROM WorkflowInstance w WHERE w.uid = :uid AND w.status = :status AND w.source = 0 ORDER BY w.createTime DESC")
    List<WorkflowInstance> findCompletedByUidAndStatusOrderByCreateTimeDesc(@Param("uid") String uid, @Param("status") WorkflowInstanceStatus status, Pageable pageable);

    @Query("SELECT w FROM WorkflowInstance w WHERE w.uid = :uid AND w.status = :status AND w.type = :type AND w.source = 0 ORDER BY w.createTime DESC")
    List<WorkflowInstance> findByUidAndStatusAndTypeOrderByCreateTimeDesc(@Param("uid") String uid, @Param("status") WorkflowInstanceStatus status, @Param("type") FolderTypeEnum type);

    @Query("SELECT w FROM WorkflowInstance w WHERE w.uid = :uid AND w.status = :status AND w.source = 0 ORDER BY w.createTime DESC")
    List<WorkflowInstance> findByUidAndStatusOrderByCreateTimeDesc(@Param("uid") String uid, @Param("status") WorkflowInstanceStatus status);

    @Query("SELECT w FROM WorkflowInstance w WHERE w.uid = :uid AND w.type = :type AND w.source = 0 ORDER BY w.createTime DESC")
    List<WorkflowInstance> findByUidAndTypeOrderByCreateTimeDesc(@Param("uid") String uid, @Param("type") FolderTypeEnum type);

    @Query("SELECT w FROM WorkflowInstance w WHERE w.status = :status AND w.type = :type AND w.source = 0 ORDER BY w.createTime DESC")
    List<WorkflowInstance> findByStatusAndTypeOrderByCreateTimeDesc(@Param("status") WorkflowInstanceStatus status, @Param("type") FolderTypeEnum type, PageRequest pageRequest);

    @Query("SELECT w FROM WorkflowInstance w WHERE w.uid = :uid AND w.status = :status AND w.createTime BETWEEN :startTime AND :endTime AND w.source = 0 ORDER BY w.createTime DESC")
    List<WorkflowInstance> findByUidAndStatusAndCreateTimeBetweenOrderByCreateTimeDesc(@Param("uid") String uid, @Param("status") WorkflowInstanceStatus status, @Param("startTime") long startTime, @Param("endTime") long endTime);

    @Query("SELECT w FROM WorkflowInstance w WHERE w.uid = :uid AND w.outputDir = :outputDir AND w.status = :status AND w.source = 0 AND w.type = :type ORDER BY w.createTime DESC")
    List<WorkflowInstance> findByUidAndOutputDirAndStatusAndTypeOrderByCreateTimeDesc(
            @Param("uid") String uid,
            @Param("outputDir") String outputDir,
            @Param("status") WorkflowInstanceStatus status,
            @Param("type") FolderTypeEnum type);

    @Query("SELECT wi.outputDir, SUM(wi.videoCount), MAX(wi.createTime) FROM WorkflowInstance wi " +
            "WHERE wi.uid = :uid AND wi.type = :type AND wi.source = 0 " +
            "GROUP BY wi.outputDir " +
            "ORDER BY MAX(wi.createTime) DESC")
    List<Object[]> findDistinctOutputDirsByUidAndTypeWithVideoCount(String uid, FolderTypeEnum type, Pageable pageable);

    @Query("SELECT w FROM WorkflowInstance w " +
            "WHERE w.uid = :uid " +
            "AND (:status IS NULL OR w.status = :status) " +
            "AND (:type IS NULL OR w.type = :type) " +
            "AND w.source <> 1 " +
            "ORDER BY w.createTime DESC")
    List<WorkflowInstance> findWithFiltersExcludeSourceOne(
            @Param("uid") String uid,
            @Param("status") WorkflowInstanceStatus status,
            @Param("type") FolderTypeEnum type,
            Pageable pageable
    );

    List<WorkflowInstance> findByUidAndWorkflowInstanceIdIn(String uid, List<String> workflowInstanceIds);

    @Query("SELECT w FROM WorkflowInstance w " +
            "WHERE w.uid = :uid " +
            "AND w.status <> 2 " +
            "AND w.type = :type " +
            "AND w.source = 1 " +
            "ORDER BY w.createTime DESC")
    List<WorkflowInstance> findNewestByUidAndType(String uid, FolderTypeEnum type, Pageable pageable);
}