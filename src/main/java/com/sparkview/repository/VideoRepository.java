package com.sparkview.repository;

import com.sparkview.persistence.Video;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: gavingeng
 * @create: 2025-04-17 10:34
 * @description:
 **/
@Repository
public interface VideoRepository extends JpaRepository<Video, Long> {

    List<Video> findByFolderId(String folderId);

    @Query("SELECT COUNT(v) FROM Video v WHERE v.folderId = :folderId")
    int countByFolderId(@Param("folderId") String folderId);

    @Query("SELECT v.folderId, COUNT(v) FROM Video v WHERE v.folderId IN :folderIds GROUP BY v.folderId")
    List<Object[]> countByFolderIds(@Param("folderIds") List<String> folderIds);

    @Query("SELECT v FROM Video v WHERE v.status = 0 ORDER BY v.folderId")
    List<Video> findAllByStatusZeroGroupByFolderId();

    @Query("SELECT v FROM Video v WHERE v.status = 0")
    default Map<String, List<Video>> findAllByStatusZeroGroupedByFolderId() {
        List<Video> videos = findAllByStatusZero();
        return videos.stream().collect(Collectors.groupingBy(Video::getFolderId));
    }

    List<Video> findAllByStatus(Integer status);

    default List<Video> findAllByStatusZero() {
        return findAllByStatus(0);
    }

    Optional<Video> findByObjectKeyAndUid(String objectKey, String uid);

    Optional<Video> findByObjectKey(String objectKey);

    List<Video> findByFolderIdAndStatusAndUid(String folderId, Integer status, String uid);

    int countByFolderIdAndStatusAndUid(String folderId, Integer status, String uid);

    List<Video> findByFolderIdAndUid(String folderId, String uid);

    @Transactional
    @Modifying
    @Query("UPDATE Video v SET v.status = :status WHERE v.id IN :videoIds")
    int updateVideosStatus(@Param("videoIds") List<String> videoIds, @Param("status") int status);

    @Query("SELECT COUNT(v) FROM Video v WHERE v.folderId= :folderId AND v.status = 3")
    int countVideosStatusByFolderId(String folderId);

    @Query(value = "SELECT cover_url FROM video WHERE folder_id = :folderId ORDER BY id ASC LIMIT 4", nativeQuery = true)
    List<String> findCoverUrlsByFolderId(@Param("folderId") String folderId);

    @Query(value = "SELECT folder_id, cover_url FROM video WHERE folder_id IN :folderIds AND cover_url IS NOT NULL ORDER BY id ASC", nativeQuery = true)
    List<Object[]> findCoverUrlsByFolderIds(@Param("folderIds") List<String> folderIds);

    List<Video> findAllByObjectKeyIn(List<String> objectKeys);

}
