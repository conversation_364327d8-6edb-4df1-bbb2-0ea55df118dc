package com.sparkview.repository;

import com.sparkview.persistence.Effects;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * @author: gavingeng
 * @create: 2025-05-15 11:48
 * @description:
 **/
@Repository
@RequiredArgsConstructor
public class EffectsDao {
    private final JdbcTemplate jdbcTemplate;

    public void batchUpsert(List<Effects> effects) {
        String sql = """
            INSERT INTO effects (uid, video_id, data, status,created_at)
            VALUES (?, ?, ?, ?, ?)
            ON CONFLICT (uid, video_id)
            DO UPDATE SET data = EXCLUDED.data, status = EXCLUDED.status
        """;

        jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                Effects effect = effects.get(i);
                ps.setString(1, effect.getUid());
                ps.setLong(2, effect.getVideoId());
                ps.setString(3, effect.getData());
                ps.setInt(4, effect.getStatus());
                ps.setLong(5, effect.getCreatedAt());
            }

            @Override
            public int getBatchSize() {
                return effects.size();
            }
        });
    }
}
