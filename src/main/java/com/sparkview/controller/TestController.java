package com.sparkview.controller;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Maps;
import com.sparkview.common.*;
import com.sparkview.common.ResponseBody;
import com.sparkview.controller.dto.VideoDto;
import com.sparkview.exception.BusinessException;
import com.sparkview.persistence.*;
import com.sparkview.repository.WorkflowInstanceRepository;
import com.sparkview.service.*;
import jakarta.xml.bind.DatatypeConverter;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.*;
import com.sparkview.controller.config.FunctionRequireAuth;
import com.sparkview.job.ScheduledTasksConfig;
import com.sparkview.repository.FolderRepository;

import kotlin.Pair;
import lombok.extern.slf4j.Slf4j;

import reactor.core.publisher.Flux;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {
    @Autowired
    private UserService userService;
    @Autowired
    private OpenAiChatService openAiChatService;
    @Autowired
    private WorkflowService workflowService;
    @Autowired
    private S3Service s3Service;
    @Autowired
    private FolderRepository folderRepository;
    @Autowired
    private CvService cvService;
    @Autowired
    private ScheduledTasksConfig scheduledTasksConfig;
    @Autowired
    private WorkflowInstanceRepository workflowInstanceRepository;
    @Autowired
    private VideoService videoService;
    @Autowired
    private PyService pyService;
    @Autowired
    private PatternService patternService;
    @Autowired
    private PrefectApiService prefectApiService;

    @Resource
    private HighlightService highlightService;

    @GetMapping("/updateWorkflow")
    @FunctionRequireAuth
    public ResponseBody<String> runScheduledTask() {
        try {
            scheduledTasksConfig.updateWorkflow();
            return ResponseBody.of("Scheduled task executed successfully");
        } catch (Exception e) {
            return ResponseBody.error("Failed to execute scheduled task: " + e.getMessage());
        }

    }

    @GetMapping("/updateVideoCount")
    @FunctionRequireAuth
    public ResponseBody<String> updateVideoCount() {
        try {
            List<WorkflowInstance> instances = workflowInstanceRepository.findAll();
            int updatedCount = 0;

            for (WorkflowInstance instance : instances) {
                if (instance.getVideoCount() == 0 && CollectionUtils.isNotEmpty(instance.getOutput())) {
                    try {
                        if (CollectionUtils.isNotEmpty(instance.getOutput())) {
                            instance.setVideoCount(instance.getOutput().size());
                            workflowService.saveWorkflowInstance(instance);
                            updatedCount++;
                        }
                    } catch (Exception e) {
                        log.error("Failed to parse output for workflow instance {}: {}",
                                instance.getWorkflowInstanceId(), e.getMessage());
                    }
                }
            }

            return ResponseBody.of("Successfully updated video count for " + updatedCount + " workflow instances");
        } catch (Exception e) {
            log.error("Error updating video counts: {}", e.getMessage(), e);
            return ResponseBody.error("Failed to update video counts: " + e.getMessage());
        }
    }

    @GetMapping("/rerunWorkflow")
    @FunctionRequireAuth
    public String rerunWorkflow(@RequestParam String id) {
        scheduledTasksConfig.rerunWorkflowInstance(id);
        return "success";
    }

    @GetMapping("/rerunEcommerce")
    @FunctionRequireAuth(role = "ADMIN")
    public String rerunEcommerce(@RequestParam String id) {
        String uid = RequestContext.getCurrentUser();
        if (uid.equalsIgnoreCase("123456")) {
            workflowService.updateWorkflowInstanceStatus(id, WorkflowInstanceStatus.PENDING, null, null, 0);
            prefectApiService.startWorkflowByPrefectApi(id, null);
        } else {
            throw new BusinessException(400, "You are not allowed to rerun this workflow");
        }
        return "success";
    }

    @GetMapping("/getEcommerceParams")
    @FunctionRequireAuth(role = "ADMIN")
    public String getEcommerceParams(@RequestParam String id) {
        String uid = RequestContext.getCurrentUser();
        workflowService.updateWorkflowInstanceStatus(id, WorkflowInstanceStatus.PENDING, null, null, 0);
        Map<String, Object> res = prefectApiService.getWorkflowParams(id, null);
        return JsonUtils.toJson(res);
    }

    @GetMapping("/toSqsMessage")
    @FunctionRequireAuth
    public String toSqsMessage(@RequestParam String id) {
        Map<String, Object> res = new HashMap<>();
        WorkflowInstance workflowInstance = workflowService.getWorkflowInstanceById(id)
                .orElseThrow(() -> new RuntimeException("error"));
        String params = workflowInstance.getParams();
        ConfigTemplate config = JsonUtils.toObject(params, ConfigTemplate.class);
        DifyService.EcommerceWorkflowDifyRequest request = pyService.toEcommerceDifyRequest(config, null);
        res.put("workflow_instance_id", id);
        res.put("params", request);
        return JsonUtils.toJson(res);
    }

    @GetMapping("/testShortDramaDifySchema")
    public String testShortDramaDifySchema(@RequestParam String id) {
        WorkflowInstance workflowInstance = workflowService.getWorkflowInstanceById(id)
                .orElseThrow(() -> new RuntimeException("error"));
        String params = workflowInstance.getParams();
        ConfigTemplate config = JsonUtils.toObject(params, ConfigTemplate.class);
        DifyService.ShortDramaWorkflowDifyRequest request = scheduledTasksConfig.toShortDramaDifyRequest(config);
        return JsonUtils.toJson(request);
    }

    @GetMapping("runShortDramaWorkflow")
    public String runShortDramaWorkflow() {
        scheduledTasksConfig.runShortDramaWorkflow();
        return "success";
    }

    @GetMapping("getWorkFlowInstancesDto")
    @FunctionRequireAuth
    public String getWorkFlowInstancesDto(@RequestParam String id) {
        // Get the workflow instance by ID
        Optional<WorkflowInstance> workflowInstance = workflowService.getWorkflowInstanceById(id);

        if (workflowInstance.isPresent()) {
            // Parse the params to get the ConfigTemplate
            ConfigTemplate config = JsonUtils.toObject(workflowInstance.get().getParams(), ConfigTemplate.class);

            // Get the videoList from the config
            String videoListStr = config.getValueByKey("videoList");
            List<String> presignedUrls = new ArrayList<>();

            if (StringUtils.isNotEmpty(videoListStr)) {
                // Parse the videoList string to get a list of object keys
                List<String> objectKeys = JsonUtils.toObject(videoListStr, new TypeReference<>() {
                });

                if (CollectionUtils.isNotEmpty(objectKeys)) {
                    // Generate presigned URLs for each object key with 24 hours validity (1 day)
                    for (String objectKey : objectKeys) {
                        if (StringUtils.isNotEmpty(objectKey)) {
                            // 24 hours * 60 minutes = 1440 minutes
                            presignedUrls.add(s3Service.generatePresignedDownloadUrl(objectKey, 1440));
                        }
                    }
                }
            }
            Map<String, Object> response = new HashMap<>();
            response.put("workflow_instance_id", id);
            response.put("video_input_files", presignedUrls);
            return JsonUtils.toJson(response);

        } else {
            return "failed";
        }
    }

    @GetMapping("getVideoUrls")
    public ResponseBody<List<String>> runShortDramaWorkflow(@RequestParam String id) {
        Optional<WorkflowInstance> workflowInstance = workflowService.getWorkflowInstanceById(id);
        if (workflowInstance.isPresent()) {
            ConfigTemplate config = JsonUtils.toObject(workflowInstance.get().getParams(), ConfigTemplate.class);
            List<String> urls = new ArrayList<>();
            String videoListStr = config.getValueByKey("videoList");
            if (StringUtils.isNotEmpty(videoListStr)) {
                List<String> videos = JsonUtils.toObject(videoListStr, new TypeReference<>() {
                });
                if (videos.stream().anyMatch(StringUtils::isEmpty)) {
                    throw new BusinessException(400, "video contains empty value");
                }
                for (String video : videos) {
                    urls.add(s3Service.generatePresignedDownloadUrl(video, 30));
                }
            }
            return ResponseBody.of(urls);
        } else {
            return ResponseBody.error("Workflow instance not found");
        }
    }


    @GetMapping("/updateFlow")
    public String updateFlow() {
        scheduledTasksConfig.updateWorkflow();
        return "success";
    }

    @PostMapping("/initializeS3Directories")
    @FunctionRequireAuth
    public ResponseBody<List<String>> initializeS3Directories() {
        // 获取当前用户ID
        String uid = RequestContext.getCurrentUser();

        // 获取用户的所有文件夹
        List<Folder> folders = folderRepository.findByUid(uid);

        // 用于存储创建的目录路径
        List<String> createdDirectories = new ArrayList<>();

        // 遍历所有文件夹并在S3中创建对应的目录
        for (Folder folder : folders) {
            // 构建S3目录路径
            String folderPath = String.format("users/%s/assets/%s/%s/",
                    uid,
                    folder.getType().name().toLowerCase(),
                    folder.getName());

            // 在S3中创建目录
            try {
                s3Service.createDirectory(folderPath);
                createdDirectories.add(folderPath);
            } catch (Exception e) {
                // 记录错误但继续处理其他文件夹
                createdDirectories.add(folderPath + " (创建失败: " + e.getMessage() + ")");
            }
        }

        return ResponseBody.of(createdDirectories);
    }

    private String extractFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }

        // 移除URL中可能存在的查询参数
        String urlWithoutParams = url;
        if (url.contains("?")) {
            urlWithoutParams = url.substring(0, url.indexOf("?"));
        }

        // 获取URL的路径部分
        String[] parts = urlWithoutParams.split("/");
        if (parts.length > 0) {
            // 返回最后一部分作为文件名
            return parts[parts.length - 1];
        }

        return "";
    }

    @GetMapping("/test")
    public String test() {
        return "Hello, World!";
    }

    @PostMapping("/createInviteUser")
    @FunctionRequireAuth
    public String createInviteUser(@RequestParam String username) {
        try {
            User user = userService.createUserWithInviteCode(username);
            Pair<String, String> pair = new Pair<>(user.getInviteCode(), user.getAccessKey());
            String res = "";
            res += "用户名: " + user.getUsername() + "\n";
            res += "邀请码(登录用户): " + user.getInviteCode() + "\n";
            res += "accessKey: " + user.getAccessKey() + "\n";
            return res;
        } catch (DataIntegrityViolationException e) {
            // 处理唯一索引冲突
            if (e.getMessage().contains("username")) {
                return "用户名已存在";
            } else if (e.getMessage().contains("invite_code")) {
                return "邀请码已存在";
            }
            return "创建用户失败: " + e.getMessage();
        }

    }

    @PostMapping("/chat")
    public String chat(@RequestBody String entity) {
        return openAiChatService.chat(entity);
    }

    @PostMapping("/stream")
    public Flux<String> stream(@RequestBody String entity) {
        return openAiChatService.stream(entity);
    }

    @GetMapping("/processOutputMetadata")
    @FunctionRequireAuth
    public ResponseBody<String> processOutputMetadata() {
        try {
            // Get all workflow instances with SUCCESS status
            List<WorkflowInstance> successInstances = workflowInstanceRepository.findByStatusOrderByCreateTimeDesc(
                    WorkflowInstanceStatus.SUCCESS, PageRequest.of(0, Integer.MAX_VALUE));

            int totalInstances = successInstances.size();
            int processedInstances = 0;
            int processedVideos = 0;
            int updatedVideos = 0;

            log.info("Starting to process metadata for {} workflow instances", totalInstances);

            for (WorkflowInstance instance : successInstances) {
                List<VideoFile> outputFiles = instance.getOutput();
                boolean instanceUpdated = false;

                if (CollectionUtils.isNotEmpty(outputFiles)) {
                    for (VideoFile videoFile : outputFiles) {
                        processedVideos++;

                        String objectKey = videoFile.getObjectKey();
                        if (StringUtils.isNotBlank(objectKey)) {
                            try {
                                // Get metadata from S3
                                S3Service.FileInfoDto fileInfo = s3Service.getFileInfo(objectKey);
                                if (fileInfo != null) {
                                    boolean videoUpdated = false;
                                    // Update size if missing
                                    if (StringUtils.isBlank(videoFile.getSize())
                                            && fileInfo.getContentLength() != null) {
                                        videoFile.setSize(fileInfo.getContentLength().toString());
                                        videoUpdated = true;
                                    }

                                    // Get metadata
                                    Map<String, String> metadata = fileInfo.getMetadata();
                                    if (metadata != null) {
                                        // Update duration if missing
                                        if (StringUtils.isBlank(videoFile.getDuration())
                                                && metadata.get("duration") != null) {
                                            videoFile.setDuration(metadata.get("duration"));
                                            videoUpdated = true;
                                        }

                                        // Update aspect ratio if missing
                                        if (StringUtils.isBlank(videoFile.getAspectRatio())
                                                && metadata.get("aspectratio") != null) {
                                            videoFile.setAspectRatio(metadata.get("aspectratio"));
                                            videoUpdated = true;
                                        }

                                        // Update cover key if missing
                                        if (StringUtils.isBlank(videoFile.getCoverKey())
                                                && metadata.get("coverkey") != null) {
                                            videoFile.setCoverKey(metadata.get("coverkey"));
                                            videoUpdated = true;
                                        }
                                    }

                                    if (videoUpdated) {
                                        instanceUpdated = true;
                                        updatedVideos++;
                                    }
                                }
                            } catch (Exception e) {
                                log.warn("Failed to process metadata for object: {}, Error: {}", objectKey,
                                        e.getMessage());
                            }
                        }
                    }

                    // Save the workflow instance if any video was updated
                    if (instanceUpdated) {
                        workflowService.saveWorkflowInstance(instance);
                        processedInstances++;
                    }
                }
            }

            String resultMessage = String.format(
                    "Processed %d videos from %d/%d workflow instances. Updated metadata for %d videos.",
                    processedVideos, processedInstances, totalInstances, updatedVideos);
            log.info(resultMessage);
            return ResponseBody.of(resultMessage);
        } catch (Exception e) {
            log.error("Error processing output metadata: {}", e.getMessage(), e);
            return ResponseBody.error("Failed to process output metadata: " + e.getMessage());
        }

    }

    @GetMapping("/generateVideoCover")
    @FunctionRequireAuth
    public ResponseBody<String> generateVideoCover(@RequestParam String objectKey) {
        try {
            // 1. 生成预签名URL，有效期10分钟
            String presignedUrl = s3Service.generatePresignedDownloadUrl(objectKey, 10);

            // 2. 使用CvService从视频URL中提取第一帧作为封面
            byte[] coverImageBytes = cvService.extractFirstFrame(presignedUrl);

            // 3. 获取视频的宽高比和时长
            String aspectRatio = cvService.getAspectRatio(presignedUrl);
            double duration = cvService.getDuration(presignedUrl);

            // 4. 计算objectKey的MD5哈希值作为封面图片的key
            String md5Hash = generateMD5(objectKey);
            String coverKey = "static/video_covers/" + md5Hash + ".jpg";

            // 5. 使用新方法直接上传byte数组
            s3Service.uploadBytes(coverImageBytes, coverKey, "image/jpeg");

            // 6. 更新原始视频对象的元数据，添加coverKey、宽高比和时长
            java.util.Map<String, String> metadata = new java.util.HashMap<>();
            metadata.put("coverKey", coverKey);
            metadata.put("aspectRatio", aspectRatio);
            metadata.put("duration", String.format("%.2f", duration)); // 保留两位小数
            s3Service.updateObjectMetadata(objectKey, metadata);

            // 7. 生成封面图片的预签名URL并返回
            String coverUrl = s3Service.generatePresignedDownloadUrl(coverKey, 60);

            // 8. 构建包含所有信息的响应
            java.util.Map<String, Object> response = new java.util.HashMap<>();
            response.put("coverUrl", coverUrl);
            response.put("aspectRatio", aspectRatio);
            response.put("duration", duration);

            return ResponseBody.of(JsonUtils.toJson(response));
        } catch (Exception e) {
            return ResponseBody.error("生成视频封面失败: " + e.getMessage());
        }
    }

    /**
     * 生成字符串的MD5哈希值
     */
    private String generateMD5(String input) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(input.getBytes());
        byte[] digest = md.digest();
        return DatatypeConverter.printHexBinary(digest).toLowerCase();
    }


    @GetMapping("startByPrefect")
    @FunctionRequireAuth(role = "ADMIN")
    public String startByPrefect(@RequestParam String id) {
        prefectApiService.startWorkflowByPrefectApi(id, new ArrayList<>());
        return "success";
    }

    @GetMapping("/highlightVideo")
    @FunctionRequireAuth(role = "ADMIN")
    public ResponseBody<String> highlightVideo(@RequestParam String folderId) {
        List<Video> videoList = videoService.findByFolderId(folderId);
        return ResponseBody.of(JsonUtils.toJson(videoList));
    }

    @Autowired
    private EffectsService effectsService;

    @Resource
    private DramaPerfectApiService dramaPerfectApiService;

    @GetMapping("/startDrama")
    @FunctionRequireAuth(role = "ADMIN")
    public String startDrama(String id) {
        dramaPerfectApiService.startDramaWorkflow(id);
        return "success";
    }

    @GetMapping("/startHighlight")
    @FunctionRequireAuth(role = "ADMIN")
    public String startHighlight(String id) {
        highlightService.startHighlightWorkflow(id);
        return "success";
    }

    @PostMapping("/acoustic")
    @FunctionRequireAuth(role = "ADMIN")
    public String acoustic(@RequestBody AcousticRequest acousticRequest) {
        try {
            Map<String, Object> res = Maps.newHashMap();
            res.put("workflow_instance_id", acousticRequest.getWorkflow_instance_id());
            res.put("videos", acousticRequest.getVideos());
            highlightService.startAcousticWorkflow(res);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "success";
    }

    @Resource
    private FeishuUtil feishuUtil;

    @GetMapping("/msg")
    @FunctionRequireAuth(role = "ADMIN")
    public String msg(String id) {
        feishuUtil.sendFeishuAlert("gavinTestMsg", 3);
        return "success";
    }

}

@Data
class AcousticRequest {
    private String workflow_instance_id;
    private List<VideoDto> videos;
}
