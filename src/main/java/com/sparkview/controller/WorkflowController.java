package com.sparkview.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sparkview.common.*;
import com.sparkview.common.ResponseBody;
import com.sparkview.common.enums.*;
import com.sparkview.controller.config.FunctionRequireAuth;
import com.sparkview.controller.dto.VideoInfoDto;
import com.sparkview.controller.models.WorkflowInstanceVo;
import com.sparkview.exception.BusinessException;
import com.sparkview.persistence.DownloadRecord;
import com.sparkview.persistence.VideoFile;
import com.sparkview.persistence.WorkflowInstance;
import com.sparkview.repository.DownloadRecordRepository;
import com.sparkview.service.*;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/workflow")
public class WorkflowController {

    @Autowired
    private ConfigTemplateService configTemplateService;
    @Autowired
    private WorkflowService workflowService;
    @Autowired
    private VideoService videoService;
    @Autowired
    private S3Service s3Service;
    @Autowired
    private DownloadRecordRepository downloadRecordRepository;
    @Autowired
    private PrefectApiService prefectApiService;

    @Resource
    private DramaPerfectApiService dramaPerfectApiService;

    @Resource
    private HighlightService highlightService;

    /**
     * 提交一个处于 pending 状态的工作流实例
     *
     * @return 创建的工作流实例
     */
    @PostMapping("/submitEcommerce")
    @FunctionRequireAuth
    public ResponseBody<WorkflowInstance> submitEcommerce(@RequestBody ConfigTemplate configTemplate) {
        WorkflowInstanceStatus status = WorkflowInstanceStatus.PENDING;
        String uid = RequestContext.getCurrentUser();

        // 验证请求参数
        String workflowName = configTemplate.getValueByKey("name");
        if (StringUtils.isEmpty(workflowName)) {
            throw new BusinessException(400, "workflow name can not be empty");
        }

        String bgmListStr = configTemplate.getValueByKey("bgmList");

        List<String> bgms = null;
        if (StringUtils.isNotEmpty(bgmListStr)) {
            List<String> bgmList = JsonUtils.toObject(bgmListStr, new TypeReference<>() {
            });
            bgms = Lists.newArrayList();
            for (String bgmUrl : bgmList) {
                boolean isValid = ValidatorUtils.isValidUrl(bgmUrl);
                if (!isValid) {
                    throw new BusinessException(400, "bgm url is invalid");
                }
//                String folderPath = String.format("users/%s/assets/api_bgm/", uid);
                String folderPath = "static/api_bgm/";
                try {
                    MultipartFile multipartFile = UrlToMultipartFileConverter.convert(bgmUrl);
                    String url = s3Service.uploadFile(multipartFile, folderPath);
                    bgms.add("https://sparkview-oregon.s3.us-west-2.amazonaws.com/" + url);
                } catch (IOException e) {
                    throw new BusinessException(400, "bgm file is invalid");
                }
            }
            //满足3个bgm的需求
            if (bgms.size() == 1) {
                bgms.add(bgms.get(0));
                bgms.add(bgms.get(0));
            } else if (bgms.size() == 2) {
                int random = new Random().nextInt(2);
                bgms.add(bgms.get(random));
            }
        }

        String outputDir = WorkflowHelper.calculateOutputDirectory(configTemplate);

        try {
            // 创建工作流实例
            WorkflowInstance instance = new WorkflowInstance();
            instance.setType(FolderTypeEnum.E_COMMERCE);
            instance.setParams(JsonUtils.toJson(configTemplate));
            instance.setWorkflowInstanceId(UUID.randomUUID().toString());
            instance.setWorkflowName(workflowName);
            instance.setUid(uid);
            instance.setStatus(status); // 设置为 pending 状态
            instance.setRetryTimes(0);
            instance.setOutputDir(outputDir);
            instance.setPresetFilenames(JsonUtils.toJson(workflowService.getFileNames(uid, workflowName, 3)));
            // 保存工作流实例
            WorkflowInstance savedInstance = workflowService.saveWorkflowInstance(instance);
            prefectApiService.startWorkflowByPrefectApi(savedInstance.getWorkflowInstanceId(), bgms);
            return ResponseBody.of(savedInstance);
        } catch (Exception e) {
            return ResponseBody.error("提交工作流实例失败: " + e.getMessage());
        }
    }

    /**
     * 上传短剧视频文件及相关配置，生成合成后的短剧视频
     *
     * @return 任务ID和状态
     */
    @PostMapping("/submitDramaWithFiles")
    @FunctionRequireAuth
    public ResponseBody<WorkflowInstance> submitDramaWithFiles(
            @RequestPart("configTemplate") ConfigTemplate configTemplate,
            @RequestPart(value = "positionAImageFile", required = false) MultipartFile positionAImageFile,
            @RequestPart(value = "positionBImageFile", required = false) MultipartFile positionBImageFile,
            @RequestPart(value = "endingVideoFile", required = false) MultipartFile endingVideoFile) {
        String uid = RequestContext.getCurrentUser();
        if (StringUtils.isEmpty(uid)) {
            return ResponseBody.error("用户未登录");
        }
        try {
            // 校验图片格式，仅允许PNG图片
            if (positionAImageFile != null && !positionAImageFile.getContentType().equals("image/png")) {
                return ResponseBody.error("A位置图片格式必须为PNG");
            }
            if (positionBImageFile != null && !positionBImageFile.getContentType().equals("image/png")) {
                return ResponseBody.error("B位置图片格式必须为PNG");
            }
            
            // 校验文本长度
            String bottomText = configTemplate.getValueByKey("bottomText");
            if (StringUtils.isNotEmpty(bottomText)) {
                int totalWeight = 0;
                
                for (char c : bottomText.toCharArray()) {
                    // 检查是否为中文字符
                    if (Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS) {
                        totalWeight += 2; // 中文字符计为2
                    } else {
                        totalWeight += 1; // 英文字符计为1
                    }
                }
                
                if (totalWeight > 40) {
                    return ResponseBody.error("底部文字总长度不能超过40（中文字符计2，英文字符计1）");
                }
            }
            
            WorkflowInstance workflowInstance = dramaPerfectApiService.startDramaWorkflowWithFiles(
                    configTemplate,
                    positionAImageFile,
                    positionBImageFile,
                    endingVideoFile
            );
            return ResponseBody.of(workflowInstance);
        } catch (Exception e) {
            log.error("提交短剧工作流失败: {}", e.getMessage(), e);
            return ResponseBody.error("提交短剧工作流失败: " + e.getMessage());
        }
    }

    //提交一个处于 pending 状态的 短剧 工作流实例
    @PostMapping("/submitDrama")
    @FunctionRequireAuth
    public ResponseBody<WorkflowInstance> submitDrama(@RequestBody ConfigTemplate configTemplate) {
        WorkflowInstance workflowInstance = dramaPerfectApiService.startDramaWorkflow(configTemplate, null);
        return ResponseBody.of(workflowInstance);
    }

    /**
     * 获取当前用户的所有工作流实例
     */
// ... existing code ...
    @GetMapping("/instances")
    @FunctionRequireAuth
    public ResponseBody<List<WorkflowInstanceVo>> getUserWorkflowInstances(
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String type,
            @RequestParam(required = false, defaultValue = "1") int page,
            @RequestParam(required = false, defaultValue = "100") int size) {
        String uid = RequestContext.getCurrentUser();
        if (StringUtils.isEmpty(uid)) {
            return ResponseBody.error("用户未登录");
        }

        WorkflowInstanceStatus statueEnum = WorkflowInstanceStatus.fromValue(status);
        FolderTypeEnum typeEnum = FolderTypeEnum.fromName(type);

        // Use zero-based pagination internally (page-1)
        List<WorkflowInstance> instances = workflowService.getWorkflowInstancesByUidWithFiltersPaginated(
                uid, statueEnum, typeEnum, page - 1, size);

        // 将实例转换为DTO
        List<WorkflowInstanceVo> voList = new ArrayList<>();
        for (WorkflowInstance instance : instances) {
            WorkflowInstanceVo dto = convertToDto(instance);
            voList.add(dto);
        }

        return ResponseBody.of(voList);
    }

    @GetMapping("/outputDirs")
    @FunctionRequireAuth
    public ResponseBody<List<WorkflowService.OutputDirDto>> getOutputDirs(
            @RequestParam String type,
            @RequestParam(required = false, defaultValue = "1") int page,
            @RequestParam(required = false, defaultValue = "10") int size) {
        String uid = RequestContext.getCurrentUser();
        if (StringUtils.isEmpty(uid)) {
            return ResponseBody.error("用户未登录");
        }

        FolderTypeEnum typeEnum = FolderTypeEnum.fromName(type);

        List<WorkflowService.OutputDirDto> instances = workflowService.getOutputDirsByPage(uid,
                Objects.requireNonNull(typeEnum), page - 1, size);

        return ResponseBody.of(instances);
    }

    @GetMapping("/getDownloadUrl")
    @FunctionRequireAuth
    public ResponseBody<String> getDownloadUrl(@RequestParam("key") String objectKey) {
        String uid = RequestContext.getCurrentUser();
        if (StringUtils.isEmpty(uid)) {
            return ResponseBody.error("用户未登录");
        }

        try {
            // 获取文件信息以验证文件存在
            S3Service.FileInfoDto fileInfo = s3Service.getFileInfo(objectKey);
            if (fileInfo == null) {
                return ResponseBody.error("文件不存在");
            }

            // 提取文件名
            String fileName = objectKey.substring(objectKey.lastIndexOf('/') + 1);

            // 创建并保存下载记录
            DownloadRecord downloadRecord = new DownloadRecord();
            downloadRecord.setUid(uid);
            downloadRecord.setObjectKey(objectKey);
            downloadRecord.setFileName(fileName);
            downloadRecord.setFileSize(fileInfo.getContentLength());
            downloadRecordRepository.save(downloadRecord);

            // 生成预签名下载URL（30分钟有效）
            String presignedUrl = s3Service.generatePresignedDownloadUrl(objectKey, 1);
            return ResponseBody.of(presignedUrl);

        } catch (Exception e) {
            log.error("生成下载链接失败: {}", e.getMessage(), e);
            return ResponseBody.error("生成下载链接失败");
        }
    }

    @GetMapping("/downloadFile")
    @FunctionRequireAuth
    public void downloadFile(
            @RequestParam("key") String objectKey,
            HttpServletResponse response) {
        String uid = RequestContext.getCurrentUser();
        if (StringUtils.isEmpty(uid)) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return;
        }

        try {
            // Get file information to determine content type and name
            S3Service.FileInfoDto fileInfo = s3Service.getFileInfo(objectKey);
            if (fileInfo == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            // Extract file name from the object key
            String fileName = objectKey.substring(objectKey.lastIndexOf('/') + 1);

            // Create and save download record
            DownloadRecord downloadRecord = new DownloadRecord();
            downloadRecord.setUid(uid);
            downloadRecord.setObjectKey(objectKey);
            downloadRecord.setFileName(fileName);
            downloadRecord.setFileSize(fileInfo.getContentLength());
            downloadRecordRepository.save(downloadRecord);

            // Set response headers
            response.setContentType(fileInfo.getContentType());
            response.setContentLengthLong(fileInfo.getContentLength());
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" +
                    java.net.URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                            .replace("+", "%20"));

            // Get the object from S3 and stream it to the response
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(s3Service.getBucketName())
                    .key(objectKey)
                    .build();

            ResponseInputStream<GetObjectResponse> s3Object = s3Service.getS3Client().getObject(getObjectRequest);
            IOUtils.copy(s3Object, response.getOutputStream());
            response.flushBuffer();

        } catch (Exception e) {
            log.error("Error downloading file from S3: {}", e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/dirFiles")
    @FunctionRequireAuth
    public ResponseBody<List<VideoFileVo>> getDirFiles(
            @RequestParam String type,
            @RequestParam String dirName,
            @RequestParam(required = false, defaultValue = "1") int page,
            @RequestParam(required = false, defaultValue = "5") int size) {
        String uid = RequestContext.getCurrentUser();
        if (StringUtils.isEmpty(uid)) {
            return ResponseBody.error("用户未登录");
        }
        FolderTypeEnum typeEnum = FolderTypeEnum.fromName(type);
        List<VideoFile> files = workflowService.getDirFiles(uid, Objects.requireNonNull(typeEnum), dirName, page - 1,
                size);

        List<VideoFileVo> videoFileVos = new ArrayList<>();
        for (VideoFile videoFile : files) {
            if (Objects.nonNull(videoFile)) {
                videoFileVos.add(VideoFileVo.from(videoFile, s3Service.getDomain()));
            } else {
                log.error("VideoFile is null for uid: {}, type: {}, dirName: {}", uid, type, dirName);
            }
        }
        return ResponseBody.of(videoFileVos);
    }

    @GetMapping("/getPlayUrl")
    @FunctionRequireAuth
    public ResponseBody<String> getDirFiles(
            @RequestParam("key") String objectKey) {
        String uid = RequestContext.getCurrentUser();
        if (StringUtils.isEmpty(uid)) {
            return ResponseBody.error("用户未登录");
        }
        String url = s3Service.generatePresignedDownloadUrl(objectKey, 30);
        return ResponseBody.of(url);
    }

    /**
     * 获取工作流实例详情
     */
    @GetMapping("/instance/{instanceId}")
    @FunctionRequireAuth
    public ResponseBody<WorkflowInstanceVo> getWorkflowInstance(@PathVariable String instanceId) {
        String uid = RequestContext.getCurrentUser();
        if (StringUtils.isEmpty(uid)) {
            return ResponseBody.error("用户未登录");
        }

        WorkflowInstance instance = workflowService.getWorkflowInstanceById(instanceId)
                .orElseThrow(() -> new BusinessException(404, "工作流实例不存在"));

        // 验证是否是当前用户的工作流实例
        if (!instance.getUid().equals(uid)) {
            return ResponseBody.error("无权访问此工作流实例");
        }
        WorkflowInstanceVo dto = new WorkflowInstanceVo();
        BeanUtils.copyProperties(instance, dto);
        List<WorkflowInstanceVo.VideoItem> videoItems = new ArrayList<>();
        WorkflowInstanceVo.VideoItem videoItem = new WorkflowInstanceVo.VideoItem();
        videoItem.setSubtitles("""
                00:00-01:00 你好啊
                01:35- 02:01 天气真不错呢!
                """);
        videoItems.add(videoItem);
        dto.setVideos(videoItems);
        return ResponseBody.of(dto);
    }

    @GetMapping("/eCommerceConfigTemplate")
    public ResponseBody<ConfigTemplate> getECommerceConfigTemplate() {
        return ResponseBody.of(configTemplateService.getEcommerceTemplate());
    }

    @GetMapping("/dramaConfigTemplate")
    public ResponseBody<ConfigTemplate> getDramaConfigTemplate() {
        return ResponseBody.of(configTemplateService.getDramTemplate());
    }

    @GetMapping("/videoCountByMonth")
    @FunctionRequireAuth
    public ResponseBody<List<MonthlyVideoCount>> countVideos() {
        String uid = RequestContext.getCurrentUser();
        try {
            // 使用新加坡时区(Asia/Singapore)
            ZoneId singaporeZone = ZoneId.of("Asia/Singapore");
            ZonedDateTime now = ZonedDateTime.now(singaporeZone);

            // 获取一年前的日期
            ZonedDateTime oneYearAgo = now.minusYears(1);

            // 将ZonedDateTime转换为long时间戳
            long startTimestamp = oneYearAgo.toInstant().toEpochMilli();
            long endTimestamp = now.toInstant().toEpochMilli();

            // 获取用户所有成功的工作流实例
            List<WorkflowInstance> successInstances = workflowService.getWorkflowInstancesByUidAndTimeRangeAndStatus(
                    uid, startTimestamp, endTimestamp, WorkflowInstanceStatus.SUCCESS);

            // 按月份分组统计视频数
            Map<YearMonth, Integer> monthlyVideoCountMap = new HashMap<>();

            // 处理视频数统计
            for (WorkflowInstance instance : successInstances) {
                // 将时间戳转换为新加坡时区的ZonedDateTime
                ZonedDateTime instanceTime = ZonedDateTime.ofInstant(
                        Instant.ofEpochMilli(instance.getCreateTime()), singaporeZone);

                // 获取年月
                YearMonth yearMonth = YearMonth.from(instanceTime);

                // 获取该实例的视频数量并累加
                int videoCount = instance.getVideoCount();
                monthlyVideoCountMap.put(yearMonth, monthlyVideoCountMap.getOrDefault(yearMonth, 0) + videoCount);
            }

            // 使用数据库直接查询每月不重复的下载量
            List<Object[]> monthlyDownloadStats = downloadRecordRepository.countUniqueObjectKeysByMonth(
                    uid, startTimestamp, endTimestamp);

            // 将数据库查询结果转换为Map
            Map<YearMonth, Integer> monthlyDownloadCountMap = new HashMap<>();
            for (Object[] result : monthlyDownloadStats) {
                int year = ((Number) result[0]).intValue();
                int month = ((Number) result[1]).intValue();
                int downloadCount = ((Number) result[2]).intValue();
                YearMonth yearMonth = YearMonth.of(year, month);
                monthlyDownloadCountMap.put(yearMonth, downloadCount);
            }

            // 合并所有年月
            Set<YearMonth> allMonths = new HashSet<>();
            allMonths.addAll(monthlyVideoCountMap.keySet());
            allMonths.addAll(monthlyDownloadCountMap.keySet());

            // 转换为结果列表
            List<MonthlyVideoCount> result = new ArrayList<>();
            for (YearMonth yearMonth : allMonths) {
                MonthlyVideoCount monthlyCount = new MonthlyVideoCount();
                monthlyCount.setYearMonth(yearMonth.toString());
                monthlyCount.setCount(monthlyVideoCountMap.getOrDefault(yearMonth, 0));
                monthlyCount.setDownloadCount(monthlyDownloadCountMap.getOrDefault(yearMonth, 0));
                result.add(monthlyCount);
            }

            // 按年月排序
            result.sort(Comparator.comparing(MonthlyVideoCount::getYearMonth));

            return ResponseBody.of(result);
        } catch (Exception e) {
            return ResponseBody.error("获取视频统计数据失败: " + e.getMessage());
        }
    }

    @GetMapping("/recreatedList")
    @FunctionRequireAuth
    public ResponseBody<List<WorkflowInstanceWithOutput>> getWorkflowOutputDirectories(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        String uid = RequestContext.getCurrentUser();
        if (StringUtils.isEmpty(uid)) {
            return ResponseBody.error("用户未登录");
        }

        try {
            // 获取用户已完成的工作流列表，按时间倒序排列
            List<WorkflowInstance> completedWorkflows = workflowService
                    .getCompletedWorkflowsByUidAndStatusPaginated(uid, WorkflowInstanceStatus.SUCCESS, page, size);

            // 解析每个工作流实例的输出
            List<WorkflowInstanceWithOutput> workflowsWithOutput = completedWorkflows.stream()
                    .map(workflow -> {
                        WorkflowInstanceWithOutput result = new WorkflowInstanceWithOutput();
                        result.setWorkflowInstance(workflow);
                        // 解析输出 JSON 为 List<String>
                        List<String> outputList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(workflow.getOutput())) {
                            for (VideoFile videoFile : workflow.getOutput()) {
                                outputList.add(videoFile.getObjectKey());
                            }
                        }
                        result.setOutputList(outputList);
                        return result;
                    })
                    .toList();
            return ResponseBody.of(workflowsWithOutput);
        } catch (Exception e) {
            return ResponseBody.error("获取已完成工作流列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/effect")
    @FunctionRequireAuth(role = "ADMIN")
    public String effect(@RequestParam("id") String id) {
        try {
            if (StringUtils.isNotEmpty(id))
                highlightService.startEffectWorkflow(id);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "success";
    }

    @GetMapping("/instancesByIds")
    @FunctionRequireAuth
    public ResponseBody<List<WorkflowInstanceVo>> getUserWorkflowInstancesByIds(
            @RequestParam String ids) {
        String uid = RequestContext.getCurrentUser();
        if (StringUtils.isEmpty(uid)) {
            return ResponseBody.error("用户未登录");
        }

        List<String> idList = Arrays.asList(ids.split(","));
        List<WorkflowInstance> instances = workflowService.getWorkflowInstancesByIds(uid, idList);

        // 将实例转换为DTO
        List<WorkflowInstanceVo> voList = new ArrayList<>();
        for (WorkflowInstance instance : instances) {
            WorkflowInstanceVo dto = convertToDto(instance);
            voList.add(dto);
        }

        return ResponseBody.of(voList);
    }

    private WorkflowInstanceVo convertToDto(WorkflowInstance instance) {
        WorkflowInstanceVo dto = new WorkflowInstanceVo();
        // 直接赋值基本属性
        dto.setWorkflowInstanceId(instance.getWorkflowInstanceId());
        dto.setWorkflowName(instance.getWorkflowName());
        dto.setUid(instance.getUid());
        dto.setStatus(instance.getStatus());
        dto.setType(instance.getType());
        dto.setOutputDir(instance.getOutputDir());
        dto.setVideoCount(instance.getVideoCount());
        dto.setSubtitles(instance.getSubtitles());
        dto.setError(instance.getError());
        dto.setCreateTime(instance.getCreateTime());
        dto.setUpdateTime(instance.getUpdateTime());

        List<WorkflowInstanceVo.VideoItem> videoItems = new ArrayList<>();
        // 从 output 中提取视频地址
        if (CollectionUtils.isNotEmpty(instance.getOutput())) {
            // 检查是否是短剧贴片任务（通过检查是否有overlay视频来判断）
            boolean hasOverlayVideos = instance.getOutput().stream()
                    .anyMatch(videoFile -> videoFile.getObjectKey() != null && 
                             videoFile.getObjectKey().contains("overlay_"));
            
            for (VideoFile videoFile : instance.getOutput()) {
                if (videoFile != null && videoFile.getObjectKey() != null) {
                    String objectKey = videoFile.getObjectKey();
                    
                    // 如果是短剧贴片任务，只显示overlay视频；否则显示所有视频
                    if (hasOverlayVideos) {
                        // 短剧贴片任务：只显示overlay视频
                        if (objectKey.contains("overlay_")) {
                            WorkflowInstanceVo.VideoItem videoItem = createVideoItemFromVideoFile(videoFile);
                            videoItems.add(videoItem);
                        }
                        // 原始视频不添加到前端显示列表中
                    } else {
                        // 普通任务：显示所有视频
                        WorkflowInstanceVo.VideoItem videoItem = createVideoItem(objectKey);
                        videoItems.add(videoItem);
                    }
                }
            }
        } else {
            if (StringUtils.isNoneEmpty(instance.getPresetFilenames())) {
                List<String> presetFileNames = JsonUtils.toObject(instance.getPresetFilenames(), new TypeReference<>() {
                });
                if (CollectionUtils.isNotEmpty(presetFileNames)) {
                    for (String fileName : presetFileNames) {
                        WorkflowInstanceVo.VideoItem videoItem = createDefaultVideoItem(fileName);
                        videoItems.add(videoItem);
                    }
                }
            }
        }
        dto.setVideos(videoItems);
        return dto;
    }

    /**
     * 从VideoFile对象创建VideoItem
     */
    private WorkflowInstanceVo.VideoItem createVideoItemFromVideoFile(VideoFile videoFile) {
        WorkflowInstanceVo.VideoItem videoItem = new WorkflowInstanceVo.VideoItem();
        String objectKey = videoFile.getObjectKey();
        
        // 设置基本属性
        videoItem.setObjectKey(objectKey);
        videoItem.setFileName(extractFileName(objectKey));
        videoItem.setAspectRatio(videoFile.getAspectRatio());
        videoItem.setDuration(videoFile.getDuration());
        videoItem.setSize(videoFile.getSize());
        
        // 设置封面URL
        if (StringUtils.isNotEmpty(videoFile.getCoverKey())) {
            videoItem.setCoverUrl(s3Service.getDomain() + "/" + videoFile.getCoverKey());
        }
        
        // 生成预签名URL用于播放
        try {
            String presignedUrl = s3Service.generatePresignedDownloadUrl(objectKey, 30);
            videoItem.setVideoUrl(presignedUrl);
        } catch (Exception e) {
            log.warn("生成预签名URL失败: {}", objectKey, e);
            videoItem.setVideoUrl(null);
        }
        
        // 设置视频名称
        videoItem.setVideoName(extractFileName(objectKey));
        videoItem.setSubtitles(null);
        
        return videoItem;
    }
    
    /**
     * 从对象键中提取文件名
     */
    private String extractFileName(String objectKey) {
        if (StringUtils.isEmpty(objectKey)) {
            return "";
        }
        return objectKey.substring(objectKey.lastIndexOf('/') + 1);
    }

    /**
     * 根据视频URL创建VideoItem（保留原方法用于兼容性）
     */
    private WorkflowInstanceVo.VideoItem createVideoItem(String objectKey) {
        WorkflowInstanceVo.VideoItem videoItem = new WorkflowInstanceVo.VideoItem();
        VideoInfoDto videoInfoDto = videoService.getVideoInfoDto(objectKey);
        // 设置共有属性
        videoItem.setObjectKey(objectKey);
        videoItem.setFileName(videoInfoDto.getFileName());
        videoItem.setAspectRatio(videoInfoDto.getAspectRatio());
        videoItem.setCoverUrl(videoInfoDto.getCoverUrl());
        videoItem.setDuration(videoInfoDto.getDuration());

        // 设置大小 - 需要将Long转换为String
        if (videoInfoDto.getSize() != null) {
            videoItem.setSize(String.valueOf(videoInfoDto.getSize()));
        }

        // 设置视频URL - 使用presignedUrl
        videoItem.setVideoUrl(videoInfoDto.getPresignedUrl());
        // 设置视频名称 - 使用fileName
        videoItem.setVideoName(videoInfoDto.getFileName());
        // subtitles属性在VideoInfoDto中不存在，默认设为null
        videoItem.setSubtitles(null);
        return videoItem;
    }

    /**
     * 创建默认的VideoItem
     */
    private WorkflowInstanceVo.VideoItem createDefaultVideoItem(final String fileName) {
        String voFileName = StringUtils.isEmpty(fileName) ? "tbd.mp4" : fileName;
        WorkflowInstanceVo.VideoItem videoItem = new WorkflowInstanceVo.VideoItem();
        videoItem.setAspectRatio("");
        videoItem.setSize("");
        videoItem.setDuration("");
        videoItem.setFileName(voFileName);
        videoItem.setCoverUrl("");
        return videoItem;
    }

    @Data
    public static class WorkflowInstanceWithOutput {
        private WorkflowInstance workflowInstance;
        private List<String> outputList;
    }

    @Data
    public static class MonthlyVideoCount {
        private String yearMonth; // 格式: yyyy-MM
        private int count;
        private int downloadCount; // 新增下载次数字段
        private String cost = "$133.33";
    }

    @Data
    public static class VideoFileVo {
        private String objectKey;
        private String size;
        private String duration;
        private String aspectRatio;
        private String cover;

        public static VideoFileVo from(VideoFile videoFile, final String domain) {
            VideoFileVo vo = new VideoFileVo();
            vo.setObjectKey(videoFile.getObjectKey());
            vo.setSize(videoFile.getSize());
            vo.setDuration(videoFile.getDuration());
            vo.setAspectRatio(videoFile.getAspectRatio());
            vo.setCover(domain + "/" + videoFile.getCoverKey());
            return vo;
        }
    }

}