package com.sparkview.controller;

import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sparkview.common.*;
import com.sparkview.common.enums.VideoStatusEnum;
import com.sparkview.common.enums.WorkflowSourceEnum;
import com.sparkview.controller.dto.VideoInfoDto;
import com.sparkview.persistence.Video;
import com.sparkview.persistence.WorkflowInstance;
import com.sparkview.repository.VideoRepository;
import com.sparkview.repository.WorkflowInstanceRepository;
import com.sparkview.service.*;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.sparkview.common.enums.FolderTypeEnum;
import com.sparkview.controller.config.FunctionRequireAuth;
import com.sparkview.exception.BusinessException;
import com.sparkview.persistence.Folder;
import com.sparkview.repository.FolderRepository;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response;
import software.amazon.awssdk.services.s3.model.S3Object;

import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/folder")
public class FolderController {

    private static final int EXPIRED_MINUTES = 20;

    @Autowired
    private FolderRepository folderRepository;

    @Autowired
    private S3Service s3Service;

    @Autowired
    private VideoService videoService;

    @Resource
    private VideoRepository videoRepository;

    @Resource
    private WorkflowService workflowService;

    @Resource
    private WorkflowInstanceRepository workflowInstanceRepository;

    @Resource
    private HighlightService highlightService;

    @Resource
    private DistributedLockService distributedLockService;

    @GetMapping("/listFolderTypes")
    public ResponseBody<List<FolderTypeEnum>> listFolderTypes() {
        List<FolderTypeEnum> folderTypes = FolderTypeEnum.allValues();
        return ResponseBody.of(folderTypes);
    }

    @PostMapping("/createFolder")
    @FunctionRequireAuth
    public ResponseBody<List<Folder>> createFolder(@RequestBody CreateFolderRequest createFolderRequest) {
        String uid = RequestContext.getCurrentUser();

        if (StringUtils.isEmpty(createFolderRequest.getFolderName())) {
            return ResponseBody.error("folder name can not be empty");
        }

        FolderTypeEnum folderType = FolderTypeEnum.fromName(createFolderRequest.getType());
        if (folderType == null) {
            return ResponseBody.error("invalid folder type");
        }

        // 创建数据库记录
        Folder folder = new Folder();
        folder.setName(createFolderRequest.getFolderName());
        folder.setType(folderType);
        folder.setUid(uid);

        try {
            folderRepository.save(folder);
        } catch (DataIntegrityViolationException e) {
            throw new BusinessException(500, "Folder name already exists, please modify.", "名称已存在，请修改");
        }

        // 在S3中创建对应的目录结构
        String folderPath = getFolderPath(uid, folderType, createFolderRequest.getFolderName());

        // 在S3中创建一个空的目录标记对象
        s3Service.createDirectory(folderPath);

        List<Folder> folders = folderRepository.findAll();
        return ResponseBody.of(folders);
    }

    @GetMapping("/listFolder")
    @FunctionRequireAuth
    public ResponseBody<List<FolderInfoVO>> listFolder(String type) {
        String uid = RequestContext.getCurrentUser();
        List<Folder> folders;
        FolderTypeEnum folderType = null;
        if (StringUtils.isEmpty(type)) {
            // 如果没有提供type参数，返回所有文件夹
            folders = folderRepository.findByUid(uid);
            if (CollectionUtils.isEmpty(folders)) {
                return ResponseBody.of(Collections.emptyList());
            }
        } else {
            // 如果提供了type参数，根据type过滤
            folderType = FolderTypeEnum.fromName(type);
            if (folderType == null) {
                return ResponseBody.error("invalid folder type");
            }
            folders = folderRepository.findByUidAndTypeOrderByCreatedAtDesc(uid, folderType);
        }

        if (folderType == FolderTypeEnum.SHORT_DRAMA) {
            // 批量获取所有文件夹的封面URL
            List<String> folderIds = folders.stream()
                    .map(folder -> String.valueOf(folder.getId()))
                    .collect(Collectors.toList());
            Map<String, List<String>> folderCoversMap = videoService.findCoverUrlsByFolderIds(folderIds);

            // 批量获取所有文件夹的文件数量
            Map<String, Integer> folderFileCountMap = new HashMap<>();
            List<Object[]> folderCounts = videoRepository.countByFolderIds(folderIds);
            for (Object[] result : folderCounts) {
                String folderId = (String) result[0];
                Long count = (Long) result[1];
                folderFileCountMap.put(folderId, count.intValue());
            }

            // 转换为包含文件数量和S3路径的VO对象
            List<FolderInfoVO> folderInfoList = folders.stream().map(folder -> {
                FolderInfoVO folderInfo = new FolderInfoVO();
                folderInfo.setId(folder.getId());
                folderInfo.setName(folder.getName());
                folderInfo.setType(folder.getType());
                folderInfo.setUid(folder.getUid());
                folderInfo.setCreatedAt(folder.getCreatedAt());

                // 从批量获取的结果中获取封面URL
                folderInfo.setCovers(folderCoversMap.getOrDefault(String.valueOf(folder.getId()), Collections.emptyList()));

                // 构建S3路径
                String s3Path = getFolderPath(uid, folder.getType(), folder.getName());
                folderInfo.setS3Path(s3Path);

                // 从批量获取的结果中获取文件数量
                folderInfo.setFileCount(folderFileCountMap.getOrDefault(String.valueOf(folder.getId()), 0));

                return folderInfo;
            }).collect(Collectors.toList());

            return ResponseBody.of(folderInfoList);
        } else {
            List<FolderInfoVO> folderInfoList = folders.stream().map(folder -> {
                FolderInfoVO folderInfo = new FolderInfoVO();
                folderInfo.setId(folder.getId());
                folderInfo.setName(folder.getName());
                folderInfo.setType(folder.getType());
                folderInfo.setUid(folder.getUid());
                folderInfo.setCreatedAt(folder.getCreatedAt());
                folderInfo.setCovers(videoService.findCoverUrlsByFolderId(String.valueOf(folder.getId())));

                // 构建S3路径
                String s3Path = getFolderPath(uid, folder.getType(), folder.getName());
                folderInfo.setS3Path(s3Path);

                // 获取文件数量
                try {
                    int fileCount = s3Service.countObjects(s3Path);
                    folderInfo.setFileCount(fileCount);
                } catch (Exception e) {
                    log.error("获取文件夹文件数量失败: {}", e.getMessage(), e);
                    folderInfo.setFileCount(0);
                }

                return folderInfo;
            }).collect(Collectors.toList());
            return ResponseBody.of(folderInfoList);
        }
    }

    /**
     * 电商使用这个接口，短剧已经不用了
     *
     * @param folderName
     * @param type
     * @return
     */
    @GetMapping("/getFolderCover")
    @FunctionRequireAuth
    public ResponseBody<String> getFolderCover(@RequestParam String folderName, @RequestParam String type) {
        String uid = RequestContext.getCurrentUser();
        if (StringUtils.isEmpty(folderName)) {
            return ResponseBody.error("folder name cannot be empty");
        }
        if (StringUtils.isEmpty(type)) {
            return ResponseBody.error("folder type cannot be empty");
        }
        // 1. 通过folderName计算s3的prefix
        String folderPath = String.format("users/%s/assets/%s/%s",
                uid,
                type,
                folderName);
        try {
            // 确保路径以斜杠结尾
            if (!folderPath.endsWith("/")) {
                folderPath = folderPath + "/";
            }
            // 列出目录中的所有对象
            ListObjectsV2Response response = s3Service.listObjects(folderPath, 4, 0);
            // 创建返回的元数据Map
            String coverFileKey = null;
            for (S3Object obj : response.contents()) {
                // 跳过目录本身和子目录
                if (obj.key().equals(folderPath) || obj.key().endsWith("/")) {
                    continue;
                }
                // 使用videoService获取文件信息
                VideoInfoDto videoInfo = videoService.getVideoInfoDtoWithPresigned(obj.key());
                // 检查是否有cover
                if (videoInfo != null && StringUtils.isNotEmpty(videoInfo.getCoverKey())) {
                    coverFileKey = videoInfo.getCoverKey();
                    break;
                }
            }

            if (StringUtils.isNotEmpty(coverFileKey)) {
                return ResponseBody.of(s3Service.generatePresignedDownloadUrl(coverFileKey, 60 * 2));
            }
            return ResponseBody.success();
        } catch (Exception e) {
            log.error("获取文件夹封面失败: {}", e.getMessage(), e);
            return ResponseBody.error("Failed to get folder cover: " + e.getMessage());
        }
    }

    @GetMapping("/listFolderFiles")
    @FunctionRequireAuth
    public ResponseBody<FolderFilesVO> listFolderFile(
            @RequestParam String folderName,
            @RequestParam String type,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String continuationToken
    ) {
        String uid = RequestContext.getCurrentUser();
        if (StringUtils.isEmpty(folderName)) {
            return ResponseBody.error("folder name cannot be empty");
        }

        if (StringUtils.isEmpty(type)) {
            return ResponseBody.error("folder type cannot be empty");
        }

        FolderTypeEnum folderType = FolderTypeEnum.fromName(type);
        if (folderType == null) {
            return ResponseBody.error("invalid folder type");
        }

        // 拼接路径，与uploadFileToFolder方法使用相同的规则
        String folderPath = getFolderPath(uid, folderType, folderName);

        // 使用VO对象包装响应
        FolderFilesVO result = new FolderFilesVO();
        if (folderType == FolderTypeEnum.SHORT_DRAMA) {
            Folder folder = folderRepository.findByUidAndTypeAndName(uid, folderType, folderName);
            List<Video> videoAll = videoService.findByFolderId(String.valueOf(folder.getId()));

            // Convert Video list to VideoInfoDto list
            List<VideoInfoDto> fileInfoList = videoAll.stream()
                    .map(video -> videoService.getVideoInfoDtoWithoutPresigned(video))
                    .collect(Collectors.toList());

            fileInfoList = VideoSortUtils.sortByObjectKeyNo(fileInfoList);

            result.setItems(fileInfoList);
            result.setNextContinuationToken("");
            result.setHasMoreResults(false);
            result.setParseInfoByItems(videoAll);
        } else {//电商
            // 如果是第一页，多请求一个元素
            int requestSize = continuationToken == null ? size + 1 : size;
            ListObjectsV2Response response = s3Service.listObjectsWithPaginationWithFilter(folderPath, requestSize, continuationToken);

            List<S3Object> objects = response.contents();

            // 如果是第一页且第一个对象是目录，跳过它
            if (continuationToken == null && !objects.isEmpty() && objects.get(0).key().endsWith("/")) {
                objects = objects.subList(1, objects.size());
            }

            // 确保不超过请求的size
            if (objects.size() > size) {
                objects = objects.subList(0, size);
            }

            // 过滤掉目录和空项，并转换为VideoInfoDto
            List<VideoInfoDto> fileInfoList = objects.stream()
                    .filter(obj -> !obj.key().endsWith("/") && !obj.key().isEmpty())
                    .map(obj -> videoService.getVideoInfoDtoWithPresigned(obj.key()))
                    .collect(Collectors.toList());


            result.setItems(fileInfoList);
            result.setNextContinuationToken(response.nextContinuationToken());
            result.setHasMoreResults(response.isTruncated());
            result.setParseInfoByItems(Lists.newArrayList());
        }

        return ResponseBody.of(result);
    }

    @PostMapping("/videoUploadedHook")
    @FunctionRequireAuth
    public ResponseBody<String> videoUploadedHook(@RequestBody UploadedHookRequest request) {
        videoService.makeVideoInfo(request.getObjectKey());
        // 立即返回成功响应
        return ResponseBody.of("submitted");
    }

    //上传完成后，前端会回调 /videoUploadedHook ，来生成video信息 makeVideoInfo()
    @PostMapping("/uploadFileToFolder")
    @FunctionRequireAuth
    public ResponseBody<Map<String, Object>> uploadFileToFolder(@RequestBody UploadFolderRequest request) {
        String uid = RequestContext.getCurrentUser();
        if (StringUtils.isEmpty(request.getFolderName())) {
            return ResponseBody.error("folder name cannot be empty");
        }

        if (StringUtils.isEmpty(request.getFolderType())) {
            return ResponseBody.error("folder type cannot be empty");
        }

        FolderTypeEnum folderType = FolderTypeEnum.fromName(request.getFolderType());
        if (folderType == null) {
            return ResponseBody.error("invalid folder type");
        }

        // 拼接路径
        String folderPath = getFolderPath(uid, folderType, request.getFolderName());

        Map<String, Object> presignedUrls = new HashMap<>();
        List<String> objectKeyList = new ArrayList<>();

        if (request.getFileNameList() != null && !request.getFileNameList().isEmpty()) {
            for (String fileName : request.getFileNameList()) {
                String key = folderPath + fileName;
                objectKeyList.add(key);

                //文件已存在，就跳过，不生成预签名URL
                if (folderType == FolderTypeEnum.E_COMMERCE) {
                    if (s3Service.doesObjectExist(key)) {
                        throw new BusinessException(400, "File already exists: " + fileName, "文件已存在:" + fileName);
                    }
                } else {
                    if (s3Service.doesObjectExist(key))
                        continue;
                }

                String presignedUrl = s3Service.generatePresignedUploadUrl(key, EXPIRED_MINUTES);
                presignedUrls.put(fileName, Map.of(
                        "url", presignedUrl,
                        "objectKey", key));
            }
        } else {
            return ResponseBody.error("file name list cannot be empty");
        }

        try {
            if (folderType == FolderTypeEnum.SHORT_DRAMA) {
                // 存video
                videoService.saveVideos(uid, objectKeyList, folderType, request.getFolderName());
            }
        } catch (Exception e) {
            log.error("uploadFileToFolder error. request: {}", JsonUtils.toJson(request), e);
            return ResponseBody.error(e.getMessage());
        }

        return ResponseBody.of(presignedUrls);
    }

    @PostMapping("/presign")
    @FunctionRequireAuth
    public ResponseBody<Map<String, Object>> presign(@RequestBody UploadFolderRequest request) {
        String uid = RequestContext.getCurrentUser();
        if (StringUtils.isEmpty(request.getFolderName())) {
            return ResponseBody.error("folder name cannot be empty");
        }

        if (StringUtils.isEmpty(request.getFolderType())) {
            return ResponseBody.error("folder type cannot be empty");
        }

        FolderTypeEnum folderType = FolderTypeEnum.fromName(request.getFolderType());
        if (folderType == null) {
            return ResponseBody.error("invalid folder type");
        }

        String folderPath = getFolderPath(uid, folderType, request.getFolderName());
        Map<String, Object> presignedUrls = Maps.newHashMap();
        if (request.getFileNameList() != null && !request.getFileNameList().isEmpty()) {
            for (String fileName : request.getFileNameList()) {
                String key = folderPath + fileName;

                //文件已存在，就跳过，不生成预签名URL
                if (folderType == FolderTypeEnum.E_COMMERCE) {
                    if (s3Service.doesObjectExist(key)) {
                        throw new BusinessException(400, "File already exists: " + fileName, "文件已存在:" + fileName);
                    }
                } else {
                    if (s3Service.doesObjectExist(key))
                        continue;
                }

                String presignedUrl = s3Service.generatePresignedUploadUrl(key, EXPIRED_MINUTES);
                presignedUrls.put(fileName, Map.of(
                        "url", presignedUrl,
                        "objectKey", key));
            }
        } else {
            return ResponseBody.error("file name list cannot be empty");
        }
        return ResponseBody.of(new HashMap<>(presignedUrls));
    }


    //一键分析
    @PostMapping("/analysis")
    @FunctionRequireAuth
    public ResponseBody<WorkflowInstance> analysis(@RequestBody AnalysisFolderRequest analysisFolderRequest) {
        if (!analysisFolderRequest.folderType.equals(FolderTypeEnum.SHORT_DRAMA.getName())) {
            return ResponseBody.error("only drama can analysis");
        }
        WorkflowInstanceStatus status = WorkflowInstanceStatus.PENDING;
        String uid = RequestContext.getCurrentUser();
        int videoCouont = videoRepository.countByFolderId(analysisFolderRequest.getFolderId());

        if (!analysisFolderRequest.isForce()) {
            WorkflowInstance existingInstance = workflowService.findNewestByUidAndType(uid, FolderTypeEnum.SHORT_DRAMA);
            if (existingInstance != null) {
                int existingVideoCount = existingInstance.getVideoCount();
                if (existingVideoCount == videoCouont) {
                    return ResponseBody.of(existingInstance);
                }
            }
        }

        String folderName = analysisFolderRequest.getFolderName();
        try {
            WorkflowInstance instance = new WorkflowInstance();
            instance.setType(FolderTypeEnum.SHORT_DRAMA);
            instance.setParams(JsonUtils.toJson(analysisFolderRequest));
            instance.setWorkflowInstanceId(UUID.randomUUID().toString());
            instance.setWorkflowName(folderName);
            instance.setUid(uid);
            instance.setStatus(status);
            instance.setRetryTimes(0);
            instance.setOutputDir(folderName);
            instance.setSource(WorkflowSourceEnum.CLIP.getCode());
            instance.setPresetFilenames(JsonUtils.toJson(workflowService.getFileNames(uid, folderName, 3)));
            instance.setVideoCount(videoCouont);
            WorkflowInstance savedInstance = workflowService.saveWorkflowInstance(instance);
            highlightService.startHighlightWorkflow(savedInstance, analysisFolderRequest.folderId, analysisFolderRequest.dramaType, analysisFolderRequest.lang);
            return ResponseBody.of(savedInstance);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseBody.error("提交高光工作流实例失败: " + e.getMessage());
        }
    }

    private String getFolderPath(String uid, FolderTypeEnum folderType, String folderName) {
        return String.format("users/%s/assets/%s/%s/",
                uid,
                folderType.getName().toLowerCase(),
                folderName);
    }

    @Data
    public static class UploadFolderRequest {
        private String folderName;
        private List<String> fileNameList;
        private String folderType;
        private String folderId;
    }

    @Data
    public static class CreateFolderRequest {
        private String folderName;
        private String type;
    }

    @Data
    public static class UploadedHookRequest {
        private String objectKey;
    }

    @Data
    public static class FolderFilesVO {
        private List<VideoInfoDto> items;
        private String nextContinuationToken;
        private boolean hasMoreResults;
        private ParseInfo parseInfo;


        public void setParseInfoByItems(List<Video> videos) {
            if (CollectionUtils.isEmpty(videos)) {
                return;
            }

            ParseInfo parseInfoResult = new ParseInfo();
            parseInfoResult.setTotalCount(videos.size());
            parseInfoResult.setSuccessCount((int) videos.stream().filter(item -> item.getStatus() == VideoStatusEnum.SUCCEED.getCode()).count());
            parseInfoResult.setFailureCount((int) videos.stream().filter(item -> item.getStatus() == VideoStatusEnum.FAILED.getCode()).count());
            parseInfoResult.setParsingCount((int) videos.stream().filter(item -> item.getStatus() == VideoStatusEnum.PROCESSING.getCode()).count());
            this.parseInfo = parseInfoResult;
        }
    }

    @Data
    public static class ParseInfo {
        private int successCount;
        private int failureCount;
        private int totalCount;
        private int parsingCount;
    }

    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class FolderInfoVO extends Folder {
        private String s3Path; // S3目录路径
        private int fileCount; // 文件数量
        private List<String> covers; //封面图片列表
    }

    @Data
    public static class AnalysisFolderRequest {
        private String folderType;
        private String folderId;
        private String folderName;
        private int dramaType;
        private String lang;
        private boolean force = false;
    }
    @Data
    public static class FolderCoverRequest {
        private String folderType;
        private List<String> folderNames;
    }

}


