package com.sparkview.controller.dto;

import com.sparkview.common.enums.HighlightEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HighlightCandidate {
    Long id;
    Long videoId;
    public String startTime;
    public String endTime;
    public HighlightEnum source; // vision / scenario / acoustic
    double priorityScore; // 用于排序，vision 用 highlightTime，scenario 用 severity，acoustic 用 score
}
