package com.sparkview.controller.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

/**
 * @author: gavingeng
 * @create: 2025-04-22 10:46
 * @description:
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VideoDto {
    private Long id;
    private String uid;
    private String folder_id;
    private String folder_name;
    private String name;
    private String object_key;
    private int drama_type;
    private String lang;
    private Integer status;
    private double duration;

    //视频的开始时间，处理高光数据
    private String startTime;
    //视频的结束时间
    private String endTime;

    // 该视频的所有高光信息
    private List<HighlightDto> highlightList;

    /**
     * 该视频的所有高光候选片段信息
     * 用于剪片逻辑处理，在日志打印和JSON序列化时会被忽略
     * 使用@JsonIgnore和@ToString.Exclude注解确保该字段不会出现在日志和JSON中
     */
    @JsonIgnore
    @ToString.Exclude
    private List<HighlightCandidate> highlightCandidates;

    // 视频需要剪掉的低光片段
    private List<TimeFragment> lowlightList;


}
