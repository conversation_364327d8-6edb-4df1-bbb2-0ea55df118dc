package com.sparkview.controller.dto;

import lombok.Data;

/**
 * @author: gavingeng
 * @create: 2025-05-16 10:52
 * @description:
 **/
@Data
public class VideoInfoDto {
    private String fileName;
    private String objectKey;  // 完整的对象键
    private Long size;
    private Long lastModified;
    private String presignedUrl; // 预签名URL
    private String coverKey;     // 封面图片的对象键
    private String coverUrl;     // 封面图片的预签名URL
    private String aspectRatio;  // 视频宽高比
    private String duration;     // 视频时长
    private int videoStatus;
}
