package com.sparkview.persistence;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: gavingeng
 * @create: 2025-04-17 10:29
 * @description:
 **/
@Data
@Entity
@Table(name = "video")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Video {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "uid", nullable = false)
    private String uid;

    @Column(name = "folder_id", nullable = false)
    private String folderId;

    @Column(name = "folder_name", nullable = false)
    private String folderName;

    @Column(name = "name", nullable = false)
    private String name;

    //废弃
    @Column(name = "path")
    private String path;

    @Column(name = "object_key")
    private String objectKey;

    // 暴力、血腥等用来做高光的时候，给大模型用
    @Column(name = "drama_type")
    private int dramaType;

    // 语言，给asr来用
    @Column(name = "lang", length = 10)
    private String lang;

    @Column(name = "status")
    @Builder.Default
    private Integer status = 0;

    @Column(name = "audio_path")
    private String audioPath;

    @Column(name = "asr_type")
    private String asrType;

    @Column(name = "asr_path")
    private String asrPath;

    @Column(name = "created_at")
    private long createdAt;

    @Column(name = "updated_at")
    private long updatedAt;

    @Column(name = "duration")
    private double duration;

    @Column(name = "aspect_ratio")
    private String aspectRatio;

    @Column(name = "cover_key")
    private String coverKey;

    @Column(name = "cover_url")
    private String coverUrl;

    @Column(name = "size")
    private Long size;

    @PrePersist
    protected void onCreate() {
        long now = System.currentTimeMillis();
        createdAt = now;
        updatedAt = now;
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = System.currentTimeMillis();
    }
}
