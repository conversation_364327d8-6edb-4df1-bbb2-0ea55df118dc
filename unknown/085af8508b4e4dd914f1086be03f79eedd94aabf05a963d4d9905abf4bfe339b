package com.sparkview.common.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.sparkview.common.RequestContext;
import lombok.Getter;

/**
 * @author: gaving<PERSON>
 * @create: 2025-04-20 20:59
 * @description:
 **/
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum VideoDurationPatternEnum {
    AUTO(1, new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "Auto");
            put(Lang.ZH_CN, "自动");
        }
    }, "需上传连续剧集大于等于5分钟", 5, null, null, null),
    SECOND_VIDEO(2, new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "2-5mins");
            put(Lang.ZH_CN, "2-5分钟");
        }
    }, "需要上传连续剧集≥5分钟", 5, 3, 2,5),
    FIVE_VIDEO(3, new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "5-10mins");
            put(<PERSON>.<PERSON>_<PERSON>, "5-10分钟");
        }
    }, "需要上传连续剧集≥10分钟", 10,8, 5,10),
    TEN_VIDEO(4, new HashMap<Lang, String>() {
        {
            put(Lang.EN_US, "10-20mins");
            put(Lang.ZH_CN, "10-20分钟");
        }
    }, "需要上传连续剧集≥20分钟", 20,15, 10,20);

    @Getter
    private final int value;
    private final Map<Lang, String> labelMap;
    @Getter
    private final String description;
    @Getter
    private final int minRequiredDuration;
    // 从连续视频尾部倒推时间点, 用于选取开始高光时间的区间[0, 总时长-backwardTime]
    @Getter
    private final Integer backwardTime;
    @Getter
    private final Integer rangeStart;
    @Getter
    private final Integer rangeEnd;



    VideoDurationPatternEnum(int value, Map<Lang, String> labelMap, String description, int minRequiredDuration, Integer backwardTime, Integer rangeStart, Integer rangeEnd) {
        this.value = value;
        this.labelMap = labelMap;
        this.description = description;
        this.minRequiredDuration = minRequiredDuration;
        this.backwardTime = backwardTime;
        this.rangeStart = rangeStart;
        this.rangeEnd = rangeEnd;
    }

    public static List<VideoDurationPatternEnum> allValues() {
        return Arrays.asList(VideoDurationPatternEnum.values());
    }

    public String getLabel() {
        // 获取系统默认语言环境
        Lang lang = Lang.fromCode(RequestContext.getCurrentLanguage());
        return getLabel(lang);
    }

    public String getLabel(Lang lang) {
        // 如果找不到对应语言的标签，则返回默认语言的标签
        return labelMap.getOrDefault(lang, labelMap.get(Lang.DEFAULT_LANG));
    }

    public static VideoDurationPatternEnum fromValue(int value) {
        for (VideoDurationPatternEnum videoDurationPatternEnum : VideoDurationPatternEnum.values()) {
            if (videoDurationPatternEnum.getValue() == value) {
                return videoDurationPatternEnum;
            }
        }
        return null;
    }

    public static Integer getBackwardTimeBy(VideoDurationPatternEnum videoDurationPatternEnum) {
        if (videoDurationPatternEnum == null){
            return null;
        }

        return videoDurationPatternEnum.getBackwardTime();
    }

}
