package com.sparkview.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.sparkview.persistence.DistributedLock;
import com.sparkview.repository.DistributedLockRepository;

import lombok.extern.slf4j.Slf4j;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class DistributedLockService {

    private final DistributedLockRepository lockRepository;
    private final String instanceId;

    public DistributedLockService(DistributedLockRepository lockRepository) {
        this.lockRepository = lockRepository;
        this.instanceId = UUID.randomUUID().toString();
        log.info("Distributed lock service initialized with instance ID: {}", instanceId);
    }

    /**
     * 尝试获取分布式锁
     * 
     * @param lockKey 锁的唯一键
     * @param leaseTimeMillis 锁的租约时间（毫秒）
     * @return 如果成功获取锁则返回true，否则返回false
     */
    @Transactional
    public boolean tryLock(String lockKey, long leaseTimeMillis) {
        long now = System.currentTimeMillis();
        long expiresAt = now + leaseTimeMillis;
        
        try {
            // 清理过期的锁
            lockRepository.deleteExpiredLocks(now);
            
            // 尝试插入新锁
            try {
                DistributedLock lock = new DistributedLock();
                lock.setLockKey(lockKey);
                lock.setLockOwner(instanceId);
                lock.setAcquiredAt(now);
                lock.setExpiresAt(expiresAt);
                lockRepository.save(lock);
                log.info("Lock acquired: {} by instance: {}", lockKey, instanceId);
                return true;
            } catch (DataIntegrityViolationException e) {
                // 如果是主键冲突，尝试接管过期的锁
                int updated = lockRepository.updateExpiredLock(lockKey, instanceId, now, expiresAt);
                if (updated > 0) {
                    log.info("Expired lock taken over: {} by instance: {}", lockKey, instanceId);
                    return true;
                }
                
                log.debug("Failed to acquire lock: {} (already held by another instance)", lockKey);
                return false;
            }
        } catch (Exception e) {
            log.error("Error trying to acquire lock: {}", lockKey, e);
            return false;
        }
    }
    
    /**
     * 释放锁（如果由当前实例持有）
     * 
     * @param lockKey 锁的唯一键
     * @return 如果成功释放锁则返回true，否则返回false
     */
    @Transactional
    public boolean unlock(String lockKey) {
        try {
            int deleted = lockRepository.releaseLock(lockKey, instanceId);
            if (deleted > 0) {
                log.info("Lock released: {} by instance: {}", lockKey, instanceId);
                return true;
            }
            
            log.warn("Failed to release lock: {} (not owned by this instance: {})", lockKey, instanceId);
            return false;
        } catch (Exception e) {
            log.error("Error trying to release lock: {}", lockKey, e);
            return false;
        }
    }
    
    /**
     * 延长锁的租约时间（如果由当前实例持有）
     * 
     * @param lockKey 锁的唯一键
     * @param leaseTimeMillis 新的租约时间（毫秒）
     * @return 如果成功延长租约则返回true，否则返回false
     */
    @Transactional
    public boolean extendLock(String lockKey, long leaseTimeMillis) {
        long now = System.currentTimeMillis();
        long newExpiresAt = now + leaseTimeMillis;
        
        try {
            int updated = lockRepository.extendLock(lockKey, instanceId, newExpiresAt, now);
            if (updated > 0) {
                log.info("Lock lease extended: {} by instance: {}", lockKey, instanceId);
                return true;
            }
            
            log.warn("Failed to extend lock lease: {} (not owned by this instance: {})", lockKey, instanceId);
            return false;
        } catch (Exception e) {
            log.error("Error trying to extend lock lease: {}", lockKey, e);
            return false;
        }
    }
    
    /**
     * 检查锁是否被持有
     * 
     * @param lockKey 锁的唯一键
     * @return 如果锁被持有且未过期则返回true，否则返回false
     */
    public boolean isLocked(String lockKey) {
        try {
            long now = System.currentTimeMillis();
            int count = lockRepository.countActiveLocks(lockKey, now);
            return count > 0;
        } catch (Exception e) {
            log.error("Error checking lock status: {}", lockKey, e);
            return false;
        }
    }
    
    /**
     * 尝试获取分布式锁（使用TimeUnit）
     * 
     * @param lockKey 锁的唯一键
     * @param leaseTime 锁的租约时间
     * @param unit 时间单位
     * @return 如果成功获取锁则返回true，否则返回false
     */
    public boolean tryLock(String lockKey, long leaseTime, TimeUnit unit) {
        return tryLock(lockKey, unit.toMillis(leaseTime));
    }
    
    /**
     * 延长锁的租约时间（使用TimeUnit）
     * 
     * @param lockKey 锁的唯一键
     * @param leaseTime 新的租约时间
     * @param unit 时间单位
     * @return 如果成功延长租约则返回true，否则返回false
     */
    public boolean extendLock(String lockKey, long leaseTime, TimeUnit unit) {
        return extendLock(lockKey, unit.toMillis(leaseTime));
    }
}