package com.sparkview.controller;

import com.sparkview.common.ResponseBody;
import com.sparkview.controller.config.FunctionRequireAuth;
import com.sparkview.service.VideoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/video")
public class VideoController {

    @Resource
    private VideoService videoService;

    /**
     * 启动元数据迁移过程
     * 将S3元数据迁移到Video表
     * 
     * @return 迁移启动结果
     */
    @PostMapping("/migrateMetadata")
    @FunctionRequireAuth
    public ResponseBody<String> migrateMetadata() {
        log.info("开始从S3迁移元数据到Video表");
        // 异步执行迁移任务
        new Thread(() -> {
            try {
                videoService.migrateS3MetadataToVideoTable();
            } catch (Exception e) {
                log.error("迁移过程中发生错误", e);
            }
        }).start();
        
        return ResponseBody.of("迁移任务已启动，请使用/video/migrationProgress接口查询进度");
    }
    
    /**
     * 获取元数据迁移进度
     * 
     * @return 当前迁移进度信息
     */
    @GetMapping("/migrationProgress")
    @FunctionRequireAuth
    public ResponseBody<Map<String, Object>> getMigrationProgress() {
        Map<String, Object> progress = videoService.getMigrationProgress();
        return ResponseBody.of(progress);
    }
} 