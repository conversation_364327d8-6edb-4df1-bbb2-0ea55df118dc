-- 创建 video 表
CREATE TABLE video (
    id BIGSERIAL PRIMARY KEY,  -- 自增主键
    uid VARCHAR(255) NOT NULL,  -- 用户ID
    folder_id VARCHAR(32) ,
    folder_name VARCHAR(255) ,  -- 文件夹名称
    name VARCHAR(255) NOT NULL,  -- 视频名称
    path VARCHAR(255) ,  -- S3存储路径
    object_key VARCHAR(255),  -- S3的信息
    drama_type int,  -- 剧目类型
    lang VARCHAR(10) ,  -- 台词语言，
    status INT ,  -- 状态
    audio_path VARCHAR(255),  -- 音频路径
    asr_type VARCHAR(10) ,
    asr_path VARCHAR(255),  -- ASR数据
    duration DOUBLE PRECISION,
    created_at bigint NOT NULL,  -- 创建时间，缺省为当前时间戳
    updated_at bigint NOT NULL  -- 更新时间，缺省为当前时间戳
);

-- 创建 highlight 表
CREATE TABLE highlight (
    id BIGSERIAL PRIMARY KEY,  -- 自增主键
    uid VARCHAR(255) NOT NULL,  -- 用户ID
    video_id BIGINT ,  -- 视频ID，外键关联到 video 表
    type INT ,  -- 类型
    data TEXT NOT NULL,  -- 高光数据
    status INT ,  -- 状态
    created_at bigint NOT NULL  -- 创建时间，缺省为当前时间戳
);

-- 创建 effects 表
CREATE TABLE effects (
    id BIGSERIAL PRIMARY KEY,  -- 自增主键
    uid VARCHAR(255) NOT NULL,  -- 用户ID
    video_id BIGINT ,  -- 视频ID，外键关联到 video 表
    data TEXT NOT NULL,-- 特效数据
    status INT ,  -- 状态
    created_at bigint NOT NULL  -- 创建时间，缺省为当前时间戳
);

-- 创建 clip_record 表
CREATE TABLE clip_record (
    id BIGSERIAL PRIMARY KEY,  -- 自增主键
    uid VARCHAR(255) NOT NULL,  -- 用户ID
    start_video_id BIGINT ,  -- 开始视频ID，外键关联到 video 表
    start_time VARCHAR(255),  -- 开始时间
    end_video_id BIGINT ,  -- 结束视频ID，外键关联到 video 表
    end_time VARCHAR(255),  -- 结束时间
    created_at bigint NOT NULL  -- 创建时间，缺省为当前时间戳
);

-- 20250508
ALTER TABLE workflow_instance ADD COLUMN source INTEGER NOT NULL DEFAULT 0;

-- 20250515
ALTER TABLE effects ADD CONSTRAINT uk_uid_video UNIQUE (uid, video_id);

-- 20250516
ALTER TABLE video ADD COLUMN aspect_ratio VARCHAR(16) ;--视频的宽高比
ALTER TABLE video ADD COLUMN cover_key VARCHAR(256) ;--视频的封面
ALTER TABLE video ADD COLUMN cover_url VARCHAR(256) ;--视频的封面
ALTER TABLE video ADD COLUMN size BIGINT ;--视频大小:字节
COMMENT ON COLUMN video.aspect_ratio IS '视频的宽高比，格式为"宽度:高度"，如"16:9"或"4:3"';
COMMENT ON COLUMN video.cover_key IS '视频封面在存储系统中的唯一标识key';
COMMENT ON COLUMN video.cover_url IS '视频封面的完整访问URL地址';
COMMENT ON COLUMN video.size IS '视频文件大小，单位为字节(byte)';
