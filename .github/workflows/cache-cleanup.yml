name: 清理历史记录和缓存
on:
  schedule:
    - cron: '0 0 * * 0'
  workflow_dispatch:

jobs:
  cleanup:
    runs-on: ubuntu-latest
    permissions:
      actions: write
      contents: read

    steps:
#      - name: 删除工作流运行记录
#        uses: Mattraks/delete-workflow-runs@v2
#        with:
#          token: ${{ secrets.GITHUB_TOKEN }}
#          repository: ${{ github.repository }}
#          retain_days: 7
#          keep_minimum_runs: 8

      - name: 清理旧缓存
        uses: actions/github-script@v6
        with:
          script: |
            const { data: caches } = await github.rest.actions.getActionsCacheList({
              owner: context.repo.owner,
              repo: context.repo.repo,
              sort: 'last_accessed_at',
              direction: 'desc'
            });
            
            // 保留最近使用的10个缓存，删除其他的
            const cachesToDelete = caches.actions_caches.slice(10);
            
            for (const cache of cachesToDelete) {
              try {
                await github.rest.actions.deleteActionsCacheById({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  cache_id: cache.id
                });
                console.log(`Deleted old cache: ${cache.key}`);
              } catch (error) {
                console.error(`Error deleting cache ${cache.key}: ${error}`);
              }
            }

      - name: 清理大缓存
        uses: actions/github-script@v6
        with:
          script: |
            const { data: caches } = await github.rest.actions.getActionsCacheList({
              owner: context.repo.owner,
              repo: context.repo.repo
            });
            
            // 删除大于100MB的缓存
            const largeCaches = caches.actions_caches.filter(cache => cache.size_in_bytes > 100 * 1024 * 1024);
            
            for (const cache of largeCaches) {
              try {
                await github.rest.actions.deleteActionsCacheById({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  cache_id: cache.id
                });
                console.log(`Deleted large cache: ${cache.key} (Size: ${cache.size_in_bytes} bytes)`);
              } catch (error) {
                console.error(`Error deleting cache ${cache.key}: ${error}`);
              }
            }