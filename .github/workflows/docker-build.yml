name: 构建和推送 Docker 镜像

on:
  push:
    branches: [ main, master, ft-k8s, test]
  workflow_dispatch:  # 添加手动触发选项

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'corretto'
          cache: maven
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver: docker-container

      - name: Maven 构建
        run: mvn -B package --file pom.xml

      - name: 登录到 GitHub 容器注册表
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 提取元数据
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ghcr.io/${{ github.repository_owner }}/sparkview-java
          tags: |
            type=raw,value=latest
            type=sha,format=short

      - name: 构建并推送 Docker 镜像
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha,scope=${{ github.workflow }}
          cache-to: type=gha,scope=${{ github.workflow }},mode=max
          platforms: linux/amd64
          provenance: false
          build-args: |
            BUILDKIT_INLINE_CACHE=1

  deploy-to-eks:
    if: github.ref == 'refs/heads/test'  # 只在 test 分支部署
    needs: build-and-push
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
      packages: write

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 配置 AWS 凭证
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region: us-west-2

      - name: 验证 AWS 凭证
        run: |
          aws sts get-caller-identity
          aws eks list-clusters

      - name: 安装 kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'

      - name: 更新 kubeconfig
        run: aws eks update-kubeconfig --name sparkview --region us-west-2

      - name: 登录 GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 验证登录状态
        run: docker logout && docker login ghcr.io -u ${{ github.actor }} -p ${{ secrets.GITHUB_TOKEN }}

      - name: 部署到 EKS
        env:
          SHORT_SHA: ${{ github.sha }}
        run: |
          NAMESPACE="sparkview-test"
          SHORT_SHA=${SHORT_SHA:0:7}  # 正确的bash截取语法
          IMAGE_TAG="sha-${SHORT_SHA}"

          GITHUB_REPO=$(echo "${{ github.repository_owner }}" | tr '[:upper:]' '[:lower:]')

          IMAGE_URI="ghcr.io/${GITHUB_REPO}/sparkview-java:${IMAGE_TAG}"

          echo "正在更新镜像: ${IMAGE_URI} 到命名空间: ${NAMESPACE}"

          # 检查 IMAGE_URI 是否为空
          if [ -z "${IMAGE_URI}" ]; then
            echo "错误: 镜像 URI 为空"
            exit 1
          fi

          # 验证镜像是否可用
          echo "验证镜像是否可用: ${IMAGE_URI}"
          if ! docker pull ${IMAGE_URI}; then
            echo "错误: 无法拉取镜像 ${IMAGE_URI}"
            exit 1
          fi

          # 更新部署
          kubectl patch deployment sparkview-java -n ${NAMESPACE} --type=json \
          -p="[{\"op\": \"replace\", \"path\": \"/spec/template/spec/containers/0/image\", \"value\": \"${IMAGE_URI}\"}]"

          # 增加进度截止时间
          kubectl patch deployment sparkview-java -n ${NAMESPACE} --type=json \
          -p='[{"op": "replace", "path": "/spec/progressDeadlineSeconds", "value": 600}]'

          # 输出 Pod 信息以便调试
          echo "检查Pod状态:"
          kubectl get pods -n ${NAMESPACE} -l app=sparkview-java -o wide
          kubectl describe deployment sparkview-java -n ${NAMESPACE}
          kubectl describe pods -n ${NAMESPACE} -l app=sparkview-java

          # 检查部署状态
          kubectl rollout status deployment/sparkview-java -n ${NAMESPACE} --timeout=300s

      - name: 部署摘要
        run: |
          echo "✅ 已部署到 test 环境"
          echo "命名空间: sparkview-test"
          echo "镜像: ghcr.io/${{ github.repository_owner }}/sparkview-java:sha-${SHORT_SHA}"
        env:
          SHORT_SHA: ${{ github.sha }}