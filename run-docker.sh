#!/bin/bash

# 默认配置
DEFAULT_PROFILE="dev"
APP_JAR="/app/app.jar"
SENTRY_ENABLED=${SENTRY_ENABLED:-false}
SENTRY_AGENT_PATH="/app/sentry-opentelemetry-agent-8.6.0.jar"
PID_FILE="/app/app.pid"
# 环境变量形式的配置文件设置
SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-$DEFAULT_PROFILE}
# 日志配置
LOG_BASE_PATH=${LOG_BASE_PATH:-/app/logs}
LOG_FILE=${LOG_FILE:-sparkview_java}

# 确保日志目录存在
function ensure_log_dir() {
    # 根据当前profile设置日志目录
    local profile=$1
    LOG_PATH="$LOG_BASE_PATH/$profile"
    
    if [ ! -d "$LOG_PATH" ]; then
        echo "创建日志目录: $LOG_PATH"
        mkdir -p "$LOG_PATH"
        chmod 777 "$LOG_PATH"
    else
        echo "使用现有日志目录: $LOG_PATH"
    fi
    
    # 导出LOG_PATH供应用使用
    export LOG_PATH
}

# 添加启动命令
function start(){
    local profile=$1
    # 命令行参数优先，否则使用环境变量
    if [ -z "$profile" ]; then
        profile=$SPRING_PROFILES_ACTIVE
    else
        # 如果命令行有参数，同时更新环境变量
        export SPRING_PROFILES_ACTIVE=$profile
    fi
    
    # 确保日志目录存在 - 传递profile参数
    ensure_log_dir $profile
    
    echo "Starting application with profile: $profile..."
    echo "日志将保存在: $LOG_PATH/$LOG_FILE.log (按小时滚动)"
    
    # 检查是否使用Sentry
    if [ "$SENTRY_ENABLED" == "true" ] && [ -f "$SENTRY_AGENT_PATH" ]; then
        echo "启用Sentry Java Agent..."
        # 设置Sentry相关的环境变量
        export JAVA_TOOL_OPTIONS="-javaagent:$SENTRY_AGENT_PATH"
        export SENTRY_PROPERTIES_FILE="/app/sentry.properties"
        export OTEL_TRACES_EXPORTER="none"
        export OTEL_METRICS_EXPORTER="none"
        export OTEL_LOGS_EXPORTER="none"
    fi

    # 启动应用 - 在前台运行，不使用nohup和&
    echo "执行命令: java $JAVA_OPTS -jar $APP_JAR (profile from SPRING_PROFILES_ACTIVE=$profile)"
    # 确保环境变量传递给子进程
    export SPRING_PROFILES_ACTIVE=$profile
    export LOG_FILE=$LOG_FILE

    # 直接运行Java应用，不使用nohup和后台运行
    exec java $JAVA_OPTS -jar $APP_JAR
}

# 添加停止命令
function stop(){
    echo "停止应用..."
    
    # 检查PID文件是否存在
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p $pid > /dev/null; then
            echo "找到应用进程，PID: $pid"
            echo "正在停止进程..."
            kill -15 $pid
            
            # 等待进程结束，最多等待30秒
            local count=0
            while ps -p $pid > /dev/null && [ $count -lt 30 ]; do
                sleep 1
                ((count++))
                echo "等待进程结束... $count/30"
            done
            
            # 如果进程仍然存在，强制终止
            if ps -p $pid > /dev/null; then
                echo "进程未能在30秒内优雅关闭，强制终止"
                kill -9 $pid
            else
                echo "进程已优雅关闭"
            fi
        else
            echo "PID文件存在但进程已停止"
        fi
        rm -f "$PID_FILE"
    else
        # 尝试查找Java进程
        local java_pid=$(ps -ef | grep java | grep app.jar | grep -v grep | awk '{print $2}')
        if [ ! -z "$java_pid" ]; then
            echo "找到Java进程，PID: $java_pid"
            kill -15 $java_pid
            sleep 5
            if ps -p $java_pid > /dev/null; then
                kill -9 $java_pid
            fi
        else
            echo "未找到应用进程"
        fi
    fi
    
    echo "停止成功"
    return 0
}

# 显示帮助信息
function show_help() {
    echo "用法: $0 操作 [profile]"
    echo ""
    echo "操作:"
    echo "  start [profile]    启动应用，可选指定profile (默认: 环境变量SPRING_PROFILES_ACTIVE或$DEFAULT_PROFILE)"
    echo "  stop               停止应用"
    echo "  restart [profile]  重启应用，可选指定profile"
    echo "  status             查看应用状态"
    echo "  help               显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  SPRING_PROFILES_ACTIVE  设置Spring激活的profile"
    echo "  SENTRY_ENABLED       设置为true启用Sentry Java Agent (需要sentry-8.6.0.jar存在)"
    echo "  LOG_BASE_PATH       日志保存目录 (默认: /app/logs)"
    echo "  LOG_FILE            日志文件名前缀 (默认: sparkview)"
    echo ""
    echo "示例:"
    echo "  $0 start           使用环境变量SPRING_PROFILES_ACTIVE中的profile启动"
    echo "  $0 start prod      使用prod profile启动"
    echo "  SPRING_PROFILES_ACTIVE=prod $0 start"
    echo "  SENTRY_ENABLED=true $0 start"
    echo "  LOG_BASE_PATH=/var/logs LOG_FILE=app $0 start"
}

# 显示应用状态
function show_status() {
    echo "应用状态:"
    
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p $pid > /dev/null; then
            local current_profile=$SPRING_PROFILES_ACTIVE
            local profile_log_path="$LOG_BASE_PATH/$current_profile"
            
            echo "状态: 运行中 (PID: $pid)"
            echo "进程信息:"
            ps -f -p $pid
            echo "当前激活的Profile: $current_profile"
            echo "日志保存位置: $profile_log_path/$LOG_FILE.log"
        else
            echo "状态: 未运行 (PID文件存在但进程已停止)"
        fi
    else
        echo "状态: 未运行 (无PID文件)"
    fi
    
    # 检查是否有应用状态文件
    if [ -f "/app/app_status" ]; then
        echo "应用配置:"
        cat /app/app_status
    fi
}

# 检查参数数量
if [ $# -lt 1 ]; then
    echo "错误: 请至少提供一个参数 (操作)"
    show_help
    exit 1
fi

# 获取操作和可能的profile
ACTION=$1
PROFILE=$2

# 执行操作
case $ACTION in
    "start")
        start $PROFILE
        ;;
    "stop")
        stop
        ;;
    "restart")
        stop && start $PROFILE
        ;;
    "status")
        show_status
        ;;
    "help")
        show_help
        ;;
    *)
        echo "未知的操作: $ACTION"
        show_help
        exit 1
        ;;
esac