# 使用多阶段构建，第一阶段为工具层
FROM amazonlinux:2 AS tools

# 安装所有需要的工具和调试工具
RUN yum update -y && \
    yum install -y \
    curl \
    wget \
    lsof \
    net-tools \
    iputils \
    procps \
    htop \
    atop \
    pstree \
    psmisc \
    tar \
    gzip \
    unzip \
    which \
    shadow-utils \
    findutils \
    nmap-ncat \
    telnet \
    less \
    vim \
    sudo && \
    yum clean all

# 安装 Amazon Corretto JDK 17
RUN rpm --import https://yum.corretto.aws/corretto.key && \
    curl -L -o /etc/yum.repos.d/corretto.repo https://yum.corretto.aws/corretto.repo && \
    yum install -y java-17-amazon-corretto-devel && \
    yum clean all


# 安装 FFmpeg 和 FFprobe (适用于AMD/x86_64架构实例)
# RUN yum install -y xz && \
#     mkdir -p /usr/local/bin && \
#     curl -L --retry 5 --retry-delay 5 --connect-timeout 30 --max-time 300 https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz -o /tmp/ffmpeg.tar.xz && \
#     cd /tmp && \
#     tar xf ffmpeg.tar.xz && \
#     cp ffmpeg-*-static/ffmpeg ffmpeg-*-static/ffprobe /usr/local/bin/ && \
#     chmod +x /usr/local/bin/ffmpeg /usr/local/bin/ffprobe && \
#     rm -rf /tmp/ffmpeg*
RUN yum install -y xz && \
    mkdir -p /usr/local/bin && \
    curl -L https://github.com/eugeneware/ffmpeg-static/releases/latest/download/ffmpeg-linux-x64 \
    -o /usr/local/bin/ffmpeg && \
    curl -L https://github.com/eugeneware/ffmpeg-static/releases/latest/download/ffprobe-linux-x64 \
    -o /usr/local/bin/ffprobe && \
    chmod +x /usr/local/bin/ffmpeg /usr/local/bin/ffprobe


# 第二阶段：应用构建
FROM tools AS app

# 设置 JAVA_HOME 环境变量
ENV JAVA_HOME=/usr/lib/jvm/java-17-amazon-corretto

# 设置工作目录
WORKDIR /app

# 创建日志目录
RUN mkdir -p /app/logs && \
    chmod 777 /app/logs

# 复制应用 JAR 文件
COPY target/*.jar /app/app.jar
COPY sentry-opentelemetry-agent-8.6.0.jar /app/sentry-opentelemetry-agent-8.6.0.jar
COPY run-docker.sh /app/run-docker.sh
COPY sentry.properties /app/sentry.properties

# 设置日志目录环境变量
ENV LOG_BASE_PATH=/app/logs
ENV LOG_FILE=sparkview_java

# 暴露应用端口
EXPOSE 8080

# 设置启动命令
ENTRYPOINT ["/app/run-docker.sh", "start"]