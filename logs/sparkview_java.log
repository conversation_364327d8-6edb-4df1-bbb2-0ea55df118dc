2025-05-20 14:19:54.301 [http-nio-8080-exec-9] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@633b9800 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:19:54.303 [http-nio-8080-exec-9] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@3fdec218 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:19:54.303 [http-nio-8080-exec-9] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@5bc0da00 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:19:54.304 [http-nio-8080-exec-9] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@6404b2e0 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:19:54.305 [http-nio-8080-exec-9] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@48302bec (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:19:54.306 [http-nio-8080-exec-9] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@1b7fef0 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:19:54.306 [http-nio-8080-exec-9] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@5cfa241c (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:19:54.307 [http-nio-8080-exec-9] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@1d281ed1 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:19:54.308 [http-nio-8080-exec-9] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@540e29d3 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:19:54.309 [http-nio-8080-exec-9] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@6a700ddc (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:20:40.813 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=47s549ms).
2025-05-20 14:48:18.905 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-20 14:48:18.915 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-20 14:48:18.919 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-20 14:48:21.077 [main] INFO  com.sparkview.SparkViewApplication - Starting SparkViewApplication using Java 17.0.15 with PID 96823 (/Users/<USER>/Code/sparkview/sparkview-java/target/classes started by vincent in /Users/<USER>/Code/sparkview/sparkview-java)
2025-05-20 14:48:21.078 [main] INFO  com.sparkview.SparkViewApplication - The following 1 profile is active: "dev"
2025-05-20 14:48:21.609 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-20 14:48:21.683 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 70 ms. Found 13 JPA repository interfaces.
2025-05-20 14:48:22.031 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-20 14:48:22.060 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 961 ms
2025-05-20 14:48:22.237 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-20 14:48:22.267 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.5.2.Final
2025-05-20 14:48:22.283 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-05-20 14:48:22.324 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-20 14:48:25.060 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@64ff0eaa
2025-05-20 14:48:25.062 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-20 14:48:25.356 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-05-20 14:48:25.996 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-20 14:48:26.422 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-20 14:48:26.423 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-20 14:48:27.208 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - SQL Error: 0, SQLState: 23505
2025-05-20 14:48:27.208 [main] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - ERROR: duplicate key value violates unique constraint "uk6hglwlkeap17hv0j2n8gqi2q"
  Detail: Key (invite_code)=(abc-sparkview) already exists.
2025-05-20 14:48:27.500 [main] ERROR com.sparkview.config.InitConfig - already exists
2025-05-20 14:48:27.612 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-20 14:48:28.303 [main] INFO  c.s.service.DistributedLockService - Distributed lock service initialized with instance ID: a82dbda7-13da-45c9-821e-0b8525099378
2025-05-20 14:48:28.329 [main] INFO  com.sparkview.common.IPUtils - IP database loaded in 7 ms
2025-05-20 14:48:28.696 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-20 14:48:28.942 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-05-20 14:48:29.452 [main] ERROR i.n.r.d.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-05-20 14:48:29.464 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-05-20 14:48:29.474 [main] INFO  com.sparkview.SparkViewApplication - Started SparkViewApplication in 8.725 seconds (process running for 9.523)
2025-05-20 14:48:29.880 [RMI TCP Connection(2)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-20 14:48:29.881 [RMI TCP Connection(2)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-20 14:50:21.315 [http-nio-8080-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@64ff0eaa (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:50:21.316 [http-nio-8080-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@2df9a145 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:50:21.316 [http-nio-8080-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@e1129a1 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:50:21.317 [http-nio-8080-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@1d3f9978 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:50:21.318 [http-nio-8080-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@1ec9db38 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:50:21.319 [http-nio-8080-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@1912a561 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:50:21.319 [http-nio-8080-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@78871b62 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:50:21.320 [http-nio-8080-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@5387e99b (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:50:21.320 [http-nio-8080-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@3c7f8c8 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:50:21.321 [http-nio-8080-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@ff27c7c (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-05-20 14:54:46.322 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-20 14:54:46.326 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-20 14:54:46.330 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
