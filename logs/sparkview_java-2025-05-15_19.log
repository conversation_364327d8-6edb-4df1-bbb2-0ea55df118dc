2025-05-15 19:50:23.766 [main] INFO  com.sparkview.SparkViewApplication - Starting SparkViewApplication using Java 17.0.15 with PID 85448 (/Users/<USER>/Code/sparkview/sparkview-java/target/classes started by vincent in /Users/<USER>/Code/sparkview/sparkview-java)
2025-05-15 19:50:23.767 [main] INFO  com.sparkview.SparkViewApplication - The following 1 profile is active: "dev"
2025-05-15 19:50:24.248 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-15 19:50:24.316 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 64 ms. Found 13 JPA repository interfaces.
2025-05-15 19:50:24.632 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-15 19:50:24.664 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 878 ms
2025-05-15 19:50:24.815 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-15 19:50:24.832 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.5.2.Final
2025-05-15 19:50:24.846 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-05-15 19:50:24.882 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-15 19:50:27.763 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@714a4ba2
2025-05-15 19:50:27.766 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-15 19:50:28.068 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-05-15 19:50:28.695 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-15 19:50:29.051 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-15 19:50:29.052 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-15 19:50:29.824 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - SQL Error: 0, SQLState: 23505
2025-05-15 19:50:29.825 [main] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - ERROR: duplicate key value violates unique constraint "uk6hglwlkeap17hv0j2n8gqi2q"
  Detail: Key (invite_code)=(abc-sparkview) already exists.
2025-05-15 19:50:30.114 [main] ERROR com.sparkview.config.InitConfig - already exists
2025-05-15 19:50:30.181 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-15 19:50:30.778 [main] INFO  c.s.service.DistributedLockService - Distributed lock service initialized with instance ID: 90badcbd-3ee5-4cfc-896e-ce45558f8db3
2025-05-15 19:50:30.803 [main] INFO  com.sparkview.common.IPUtils - IP database loaded in 9 ms
2025-05-15 19:50:31.095 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-15 19:50:31.291 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-05-15 19:50:31.746 [main] ERROR i.n.r.d.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-05-15 19:50:31.753 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-05-15 19:50:31.762 [main] INFO  com.sparkview.SparkViewApplication - Started SparkViewApplication in 8.303 seconds (process running for 9.03)
2025-05-15 19:50:32.184 [RMI TCP Connection(4)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-15 19:50:32.185 [RMI TCP Connection(4)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
